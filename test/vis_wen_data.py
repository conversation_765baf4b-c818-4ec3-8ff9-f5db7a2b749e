import os
import sys
import argparse
import open3d as o3d

from tools.data_converter.wenyuan_converter import Wenyuan2HV, label_list

test_data_path = 'data/we_data_single_clip'

converter = Wenyuan2HV(
    root_path=test_data_path,
    label_list=label_list,
    with_camera=True,
    with_lidar=True,
    max_sweeps=1
)

sample = converter.samples[0]
sample_infos = converter.createInfos([sample])
sample_info = sample_infos[0]
points = converter._read_pcd_file(sample_info['lidar_path'])
if points is not None:
    print(f"✓ Successfully loaded {points.shape[0]} points")
    print(f"  Point cloud shape: {points.shape}")
    print(f"  X range: [{points[:, 0].min():.2f}, {points[:, 0].max():.2f}]")
    print(f"  Y range: [{points[:, 1].min():.2f}, {points[:, 1].max():.2f}]")
    print(f"  Z range: [{points[:, 2].min():.2f}, {points[:, 2].max():.2f}]")
else:
    print("✗ Failed to load point cloud")

pcd = o3d.geometry.PointCloud()
pcd.points = o3d.utility.Vector3dVector(points)  # 只用前1000个点测试
colors = [[0.5, 0.5, 0.5] for _ in points]
pcd.colors = o3d.utility.Vector3dVector(colors)
coord_frame = o3d.geometry.TriangleMesh.create_coordinate_frame(size=5.0, origin=[0, 0, 0])

converter.visualize_pointcloud_with_boxes(sample_info, max_points=1000000)