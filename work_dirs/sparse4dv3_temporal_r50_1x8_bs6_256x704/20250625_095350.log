2025-06-25 09:53:50,388 - mmdet - INFO - Environment info:
------------------------------------------------------------
sys.platform: linux
Python: 3.10.18 (main, Jun  5 2025, 13:14:17) [GCC 11.2.0]
CUDA available: True
GPU 0: NVIDIA RTX 2000 Ada Generation
CUDA_HOME: /usr/local/cuda
NVCC: Cuda compilation tools, release 11.6, V11.6.124
GCC: gcc (Ubuntu 9.4.0-1ubuntu1~20.04.2) 9.4.0
PyTorch: 1.13.0+cu116
PyTorch compiling details: PyTorch built with:
  - GCC 9.3
  - C++ Version: 201402
  - Intel(R) Math Kernel Library Version 2020.0.0 Product Build 20191122 for Intel(R) 64 architecture applications
  - Intel(R) MKL-DNN v2.6.0 (Git Hash 52b5f107dd9cf10910aaa19cb47f3abf9b349815)
  - OpenMP 201511 (a.k.a. OpenMP 4.5)
  - LAPACK is enabled (usually provided by MKL)
  - NNPACK is enabled
  - CPU capability usage: AVX2
  - CUDA Runtime 11.6
  - NVCC architecture flags: -gencode;arch=compute_37,code=sm_37;-gencode;arch=compute_50,code=sm_50;-gencode;arch=compute_60,code=sm_60;-gencode;arch=compute_70,code=sm_70;-gencode;arch=compute_75,code=sm_75;-gencode;arch=compute_80,code=sm_80;-gencode;arch=compute_86,code=sm_86
  - CuDNN 8.3.2  (built against CUDA 11.5)
  - Magma 2.6.1
  - Build settings: BLAS_INFO=mkl, BUILD_TYPE=Release, CUDA_VERSION=11.6, CUDNN_VERSION=8.3.2, CXX_COMPILER=/opt/rh/devtoolset-9/root/usr/bin/c++, CXX_FLAGS= -fabi-version=11 -Wno-deprecated -fvisibility-inlines-hidden -DUSE_PTHREADPOOL -fopenmp -DNDEBUG -DUSE_KINETO -DUSE_FBGEMM -DUSE_QNNPACK -DUSE_PYTORCH_QNNPACK -DUSE_XNNPACK -DSYMBOLICATE_MOBILE_DEBUG_HANDLE -DEDGE_PROFILER_USE_KINETO -O2 -fPIC -Wno-narrowing -Wall -Wextra -Werror=return-type -Werror=non-virtual-dtor -Wno-missing-field-initializers -Wno-type-limits -Wno-array-bounds -Wno-unknown-pragmas -Wunused-local-typedefs -Wno-unused-parameter -Wno-unused-function -Wno-unused-result -Wno-strict-overflow -Wno-strict-aliasing -Wno-error=deprecated-declarations -Wno-stringop-overflow -Wno-psabi -Wno-error=pedantic -Wno-error=redundant-decls -Wno-error=old-style-cast -fdiagnostics-color=always -faligned-new -Wno-unused-but-set-variable -Wno-maybe-uninitialized -fno-math-errno -fno-trapping-math -Werror=format -Werror=cast-function-type -Wno-stringop-overflow, LAPACK_INFO=mkl, PERF_WITH_AVX=1, PERF_WITH_AVX2=1, PERF_WITH_AVX512=1, TORCH_VERSION=1.13.0, USE_CUDA=ON, USE_CUDNN=ON, USE_EXCEPTION_PTR=1, USE_GFLAGS=OFF, USE_GLOG=OFF, USE_MKL=ON, USE_MKLDNN=ON, USE_MPI=OFF, USE_NCCL=ON, USE_NNPACK=ON, USE_OPENMP=ON, USE_ROCM=OFF, 

TorchVision: 0.14.0+cu116
OpenCV: 4.11.0
MMCV: 1.7.1
MMCV Compiler: GCC 9.4
MMCV CUDA Compiler: 11.6
MMDetection: 2.28.2+unknown
------------------------------------------------------------

2025-06-25 09:53:51,436 - mmdet - INFO - Distributed training: False
2025-06-25 09:53:52,462 - mmdet - INFO - Config:
plugin = True
plugin_dir = 'projects/mmdet3d_plugin/'
dist_params = dict(backend='nccl')
log_level = 'INFO'
work_dir = './work_dirs/sparse4dv3_temporal_r50_1x8_bs6_256x704'
total_batch_size = 48
num_gpus = 8
batch_size = 6
num_iters_per_epoch = 586
num_epochs = 100
checkpoint_epoch_interval = 20
checkpoint_config = dict(interval=11720)
log_config = dict(
    interval=51,
    hooks=[
        dict(type='TextLoggerHook', by_epoch=False),
        dict(type='TensorboardLoggerHook')
    ])
load_from = None
resume_from = None
workflow = [('train', 1)]
fp16 = dict(loss_scale=32.0)
input_shape = (704, 256)
tracking_test = True
tracking_threshold = 0.2
class_names = [
    'car', 'truck', 'construction_vehicle', 'bus', 'trailer', 'barrier',
    'motorcycle', 'bicycle', 'pedestrian', 'traffic_cone'
]
num_classes = 10
embed_dims = 256
num_groups = 8
num_decoder = 6
num_single_frame_decoder = 1
use_deformable_func = True
strides = [4, 8, 16, 32]
num_levels = 4
num_depth_layers = 3
drop_out = 0.1
temporal = True
decouple_attn = True
with_quality_estimation = True
model = dict(
    type='Sparse4D',
    use_grid_mask=True,
    use_deformable_func=True,
    img_backbone=dict(
        type='ResNet',
        depth=50,
        num_stages=4,
        frozen_stages=-1,
        norm_eval=False,
        style='pytorch',
        with_cp=True,
        out_indices=(0, 1, 2, 3),
        norm_cfg=dict(type='BN', requires_grad=True),
        pretrained='ckpt/resnet50-19c8e357.pth'),
    img_neck=dict(
        type='FPN',
        num_outs=4,
        start_level=0,
        out_channels=256,
        add_extra_convs='on_output',
        relu_before_extra_convs=True,
        in_channels=[256, 512, 1024, 2048]),
    depth_branch=dict(
        type='DenseDepthNet',
        embed_dims=256,
        num_depth_layers=3,
        loss_weight=0.2),
    head=dict(
        type='Sparse4DHead',
        cls_threshold_to_reg=0.05,
        decouple_attn=True,
        instance_bank=dict(
            type='InstanceBank',
            num_anchor=900,
            embed_dims=256,
            anchor='nuscenes_kmeans900.npy',
            anchor_handler=dict(type='SparseBox3DKeyPointsGenerator'),
            num_temp_instances=600,
            confidence_decay=0.6,
            feat_grad=False),
        anchor_encoder=dict(
            type='SparseBox3DEncoder',
            vel_dims=3,
            embed_dims=[128, 32, 32, 64],
            mode='cat',
            output_fc=False,
            in_loops=1,
            out_loops=4),
        num_single_frame_decoder=1,
        operation_order=[
            'deformable', 'ffn', 'norm', 'refine', 'temp_gnn', 'gnn', 'norm',
            'deformable', 'ffn', 'norm', 'refine', 'temp_gnn', 'gnn', 'norm',
            'deformable', 'ffn', 'norm', 'refine', 'temp_gnn', 'gnn', 'norm',
            'deformable', 'ffn', 'norm', 'refine', 'temp_gnn', 'gnn', 'norm',
            'deformable', 'ffn', 'norm', 'refine', 'temp_gnn', 'gnn', 'norm',
            'deformable', 'ffn', 'norm', 'refine'
        ],
        temp_graph_model=dict(
            type='MultiheadAttention',
            embed_dims=512,
            num_heads=8,
            batch_first=True,
            dropout=0.1),
        graph_model=dict(
            type='MultiheadAttention',
            embed_dims=512,
            num_heads=8,
            batch_first=True,
            dropout=0.1),
        norm_layer=dict(type='LN', normalized_shape=256),
        ffn=dict(
            type='AsymmetricFFN',
            in_channels=512,
            pre_norm=dict(type='LN'),
            embed_dims=256,
            feedforward_channels=1024,
            num_fcs=2,
            ffn_drop=0.1,
            act_cfg=dict(type='ReLU', inplace=True)),
        deformable_model=dict(
            type='DeformableFeatureAggregation',
            embed_dims=256,
            num_groups=8,
            num_levels=4,
            num_cams=6,
            attn_drop=0.15,
            use_deformable_func=True,
            use_camera_embed=True,
            residual_mode='cat',
            kps_generator=dict(
                type='SparseBox3DKeyPointsGenerator',
                num_learnable_pts=6,
                fix_scale=[[0, 0, 0], [0.45, 0, 0], [-0.45, 0, 0],
                           [0, 0.45, 0], [0, -0.45, 0], [0, 0, 0.45],
                           [0, 0, -0.45]])),
        refine_layer=dict(
            type='SparseBox3DRefinementModule',
            embed_dims=256,
            num_cls=10,
            refine_yaw=True,
            with_quality_estimation=True),
        sampler=dict(
            type='SparseBox3DTarget',
            num_dn_groups=5,
            num_temp_dn_groups=3,
            dn_noise_scale=[2.0, 2.0, 2.0, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5],
            max_dn_gt=32,
            add_neg_dn=True,
            cls_weight=2.0,
            box_weight=0.25,
            reg_weights=[2.0, 2.0, 2.0, 0.5, 0.5, 0.5, 0.0, 0.0, 0.0, 0.0],
            cls_wise_reg_weights=dict(
                {9: [2.0, 2.0, 2.0, 1.0, 1.0, 1.0, 0.0, 0.0, 1.0, 1.0]})),
        loss_cls=dict(
            type='FocalLoss',
            use_sigmoid=True,
            gamma=2.0,
            alpha=0.25,
            loss_weight=2.0),
        loss_reg=dict(
            type='SparseBox3DLoss',
            loss_box=dict(type='L1Loss', loss_weight=0.25),
            loss_centerness=dict(type='CrossEntropyLoss', use_sigmoid=True),
            loss_yawness=dict(type='GaussianFocalLoss'),
            cls_allow_reverse=[5]),
        decoder=dict(type='SparseBox3DDecoder'),
        reg_weights=[2.0, 2.0, 2.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0]))
dataset_type = 'NuScenes3DDetTrackDataset'
data_root = 'data/nuscenes/'
anno_root = 'data/nuscenes_anno_pkls/'
file_client_args = dict(backend='disk')
img_norm_cfg = dict(
    mean=[123.675, 116.28, 103.53], std=[58.395, 57.12, 57.375], to_rgb=True)
train_pipeline = [
    dict(type='LoadMultiViewImageFromFiles', to_float32=True),
    dict(
        type='LoadPointsFromFile',
        coord_type='LIDAR',
        load_dim=5,
        use_dim=5,
        file_client_args=dict(backend='disk')),
    dict(type='ResizeCropFlipImage'),
    dict(type='MultiScaleDepthMapGenerator', downsample=[4, 8, 16]),
    dict(type='BBoxRotation'),
    dict(type='PhotoMetricDistortionMultiViewImage'),
    dict(
        type='NormalizeMultiviewImage',
        mean=[123.675, 116.28, 103.53],
        std=[58.395, 57.12, 57.375],
        to_rgb=True),
    dict(
        type='CircleObjectRangeFilter',
        class_dist_thred=[55, 55, 55, 55, 55, 55, 55, 55, 55, 55]),
    dict(
        type='InstanceNameFilter',
        classes=[
            'car', 'truck', 'construction_vehicle', 'bus', 'trailer',
            'barrier', 'motorcycle', 'bicycle', 'pedestrian', 'traffic_cone'
        ]),
    dict(type='NuScenesSparse4DAdaptor'),
    dict(
        type='Collect',
        keys=[
            'img', 'timestamp', 'projection_mat', 'image_wh', 'gt_depth',
            'focal', 'gt_bboxes_3d', 'gt_labels_3d'
        ],
        meta_keys=['T_global', 'T_global_inv', 'timestamp', 'instance_id'])
]
test_pipeline = [
    dict(type='LoadMultiViewImageFromFiles', to_float32=True),
    dict(type='ResizeCropFlipImage'),
    dict(
        type='NormalizeMultiviewImage',
        mean=[123.675, 116.28, 103.53],
        std=[58.395, 57.12, 57.375],
        to_rgb=True),
    dict(type='NuScenesSparse4DAdaptor'),
    dict(
        type='Collect',
        keys=['img', 'timestamp', 'projection_mat', 'image_wh'],
        meta_keys=['T_global', 'T_global_inv', 'timestamp'])
]
input_modality = dict(
    use_lidar=False,
    use_camera=True,
    use_radar=False,
    use_map=False,
    use_external=False)
data_basic_config = dict(
    type='NuScenes3DDetTrackDataset',
    data_root='data/nuscenes/',
    classes=[
        'car', 'truck', 'construction_vehicle', 'bus', 'trailer', 'barrier',
        'motorcycle', 'bicycle', 'pedestrian', 'traffic_cone'
    ],
    modality=dict(
        use_lidar=False,
        use_camera=True,
        use_radar=False,
        use_map=False,
        use_external=False),
    version='v1.0-mini')
data_aug_conf = dict(
    resize_lim=(0.4, 0.47),
    final_dim=(256, 704),
    bot_pct_lim=(0.0, 0.0),
    rot_lim=(-5.4, 5.4),
    H=900,
    W=1600,
    rand_flip=True,
    rot3d_range=[-0.3925, 0.3925])
data = dict(
    samples_per_gpu=6,
    workers_per_gpu=6,
    train=dict(
        type='NuScenes3DDetTrackDataset',
        data_root='data/nuscenes/',
        classes=[
            'car', 'truck', 'construction_vehicle', 'bus', 'trailer',
            'barrier', 'motorcycle', 'bicycle', 'pedestrian', 'traffic_cone'
        ],
        modality=dict(
            use_lidar=False,
            use_camera=True,
            use_radar=False,
            use_map=False,
            use_external=False),
        version='v1.0-mini',
        ann_file='data/nuscenes_anno_pkls/nuscenes-mini_infos_train.pkl',
        pipeline=[
            dict(type='LoadMultiViewImageFromFiles', to_float32=True),
            dict(
                type='LoadPointsFromFile',
                coord_type='LIDAR',
                load_dim=5,
                use_dim=5,
                file_client_args=dict(backend='disk')),
            dict(type='ResizeCropFlipImage'),
            dict(type='MultiScaleDepthMapGenerator', downsample=[4, 8, 16]),
            dict(type='BBoxRotation'),
            dict(type='PhotoMetricDistortionMultiViewImage'),
            dict(
                type='NormalizeMultiviewImage',
                mean=[123.675, 116.28, 103.53],
                std=[58.395, 57.12, 57.375],
                to_rgb=True),
            dict(
                type='CircleObjectRangeFilter',
                class_dist_thred=[55, 55, 55, 55, 55, 55, 55, 55, 55, 55]),
            dict(
                type='InstanceNameFilter',
                classes=[
                    'car', 'truck', 'construction_vehicle', 'bus', 'trailer',
                    'barrier', 'motorcycle', 'bicycle', 'pedestrian',
                    'traffic_cone'
                ]),
            dict(type='NuScenesSparse4DAdaptor'),
            dict(
                type='Collect',
                keys=[
                    'img', 'timestamp', 'projection_mat', 'image_wh',
                    'gt_depth', 'focal', 'gt_bboxes_3d', 'gt_labels_3d'
                ],
                meta_keys=[
                    'T_global', 'T_global_inv', 'timestamp', 'instance_id'
                ])
        ],
        test_mode=False,
        data_aug_conf=dict(
            resize_lim=(0.4, 0.47),
            final_dim=(256, 704),
            bot_pct_lim=(0.0, 0.0),
            rot_lim=(-5.4, 5.4),
            H=900,
            W=1600,
            rand_flip=True,
            rot3d_range=[-0.3925, 0.3925]),
        with_seq_flag=True,
        sequences_split_num=2,
        keep_consistent_seq_aug=True),
    val=dict(
        type='NuScenes3DDetTrackDataset',
        data_root='data/nuscenes/',
        classes=[
            'car', 'truck', 'construction_vehicle', 'bus', 'trailer',
            'barrier', 'motorcycle', 'bicycle', 'pedestrian', 'traffic_cone'
        ],
        modality=dict(
            use_lidar=False,
            use_camera=True,
            use_radar=False,
            use_map=False,
            use_external=False),
        version='v1.0-mini',
        ann_file='data/nuscenes_anno_pkls/nuscenes-mini_infos_val.pkl',
        pipeline=[
            dict(type='LoadMultiViewImageFromFiles', to_float32=True),
            dict(type='ResizeCropFlipImage'),
            dict(
                type='NormalizeMultiviewImage',
                mean=[123.675, 116.28, 103.53],
                std=[58.395, 57.12, 57.375],
                to_rgb=True),
            dict(type='NuScenesSparse4DAdaptor'),
            dict(
                type='Collect',
                keys=['img', 'timestamp', 'projection_mat', 'image_wh'],
                meta_keys=['T_global', 'T_global_inv', 'timestamp'])
        ],
        data_aug_conf=dict(
            resize_lim=(0.4, 0.47),
            final_dim=(256, 704),
            bot_pct_lim=(0.0, 0.0),
            rot_lim=(-5.4, 5.4),
            H=900,
            W=1600,
            rand_flip=True,
            rot3d_range=[-0.3925, 0.3925]),
        test_mode=True,
        tracking=True,
        tracking_threshold=0.2),
    test=dict(
        type='NuScenes3DDetTrackDataset',
        data_root='data/nuscenes/',
        classes=[
            'car', 'truck', 'construction_vehicle', 'bus', 'trailer',
            'barrier', 'motorcycle', 'bicycle', 'pedestrian', 'traffic_cone'
        ],
        modality=dict(
            use_lidar=False,
            use_camera=True,
            use_radar=False,
            use_map=False,
            use_external=False),
        version='v1.0-mini',
        ann_file='data/nuscenes_anno_pkls/nuscenes-mini_infos_val.pkl',
        pipeline=[
            dict(type='LoadMultiViewImageFromFiles', to_float32=True),
            dict(type='ResizeCropFlipImage'),
            dict(
                type='NormalizeMultiviewImage',
                mean=[123.675, 116.28, 103.53],
                std=[58.395, 57.12, 57.375],
                to_rgb=True),
            dict(type='NuScenesSparse4DAdaptor'),
            dict(
                type='Collect',
                keys=['img', 'timestamp', 'projection_mat', 'image_wh'],
                meta_keys=['T_global', 'T_global_inv', 'timestamp'])
        ],
        data_aug_conf=dict(
            resize_lim=(0.4, 0.47),
            final_dim=(256, 704),
            bot_pct_lim=(0.0, 0.0),
            rot_lim=(-5.4, 5.4),
            H=900,
            W=1600,
            rand_flip=True,
            rot3d_range=[-0.3925, 0.3925]),
        test_mode=True,
        tracking=True,
        tracking_threshold=0.2))
optimizer = dict(
    type='AdamW',
    lr=0.0006,
    weight_decay=0.001,
    paramwise_cfg=dict(custom_keys=dict(img_backbone=dict(lr_mult=0.5))))
optimizer_config = dict(grad_clip=dict(max_norm=25, norm_type=2))
lr_config = dict(
    policy='CosineAnnealing',
    warmup='linear',
    warmup_iters=500,
    warmup_ratio=0.3333333333333333,
    min_lr_ratio=0.001)
runner = dict(type='IterBasedRunner', max_iters=58600)
vis_pipeline = [
    dict(type='LoadMultiViewImageFromFiles', to_float32=True),
    dict(type='Collect', keys=['img'], meta_keys=['timestamp', 'lidar2img'])
]
evaluation = dict(
    interval=11720,
    pipeline=[
        dict(type='LoadMultiViewImageFromFiles', to_float32=True),
        dict(
            type='Collect', keys=['img'], meta_keys=['timestamp', 'lidar2img'])
    ])
gpu_ids = range(0, 1)

2025-06-25 09:53:52,462 - mmdet - INFO - Set random seed to 0, deterministic: False
2025-06-25 09:53:55,058 - mmdet - INFO - Model:
Sparse4D(
  (img_backbone): ResNet(
    (conv1): Conv2d(3, 64, kernel_size=(7, 7), stride=(2, 2), padding=(3, 3), bias=False)
    (bn1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
    (relu): ReLU(inplace=True)
    (maxpool): MaxPool2d(kernel_size=3, stride=2, padding=1, dilation=1, ceil_mode=False)
    (layer1): ResLayer(
      (0): Bottleneck(
        (conv1): Conv2d(64, 64, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(64, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
        (downsample): Sequential(
          (0): Conv2d(64, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        )
      )
      (1): Bottleneck(
        (conv1): Conv2d(256, 64, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(64, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
      )
      (2): Bottleneck(
        (conv1): Conv2d(256, 64, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(64, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
      )
    )
    (layer2): ResLayer(
      (0): Bottleneck(
        (conv1): Conv2d(256, 128, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(128, 128, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(128, 512, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
        (downsample): Sequential(
          (0): Conv2d(256, 512, kernel_size=(1, 1), stride=(2, 2), bias=False)
          (1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        )
      )
      (1): Bottleneck(
        (conv1): Conv2d(512, 128, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(128, 512, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
      )
      (2): Bottleneck(
        (conv1): Conv2d(512, 128, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(128, 512, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
      )
      (3): Bottleneck(
        (conv1): Conv2d(512, 128, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(128, 512, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
      )
    )
    (layer3): ResLayer(
      (0): Bottleneck(
        (conv1): Conv2d(512, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(256, 256, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(256, 1024, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
        (downsample): Sequential(
          (0): Conv2d(512, 1024, kernel_size=(1, 1), stride=(2, 2), bias=False)
          (1): BatchNorm2d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        )
      )
      (1): Bottleneck(
        (conv1): Conv2d(1024, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(256, 1024, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
      )
      (2): Bottleneck(
        (conv1): Conv2d(1024, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(256, 1024, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
      )
      (3): Bottleneck(
        (conv1): Conv2d(1024, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(256, 1024, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
      )
      (4): Bottleneck(
        (conv1): Conv2d(1024, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(256, 1024, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
      )
      (5): Bottleneck(
        (conv1): Conv2d(1024, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(256, 1024, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
      )
    )
    (layer4): ResLayer(
      (0): Bottleneck(
        (conv1): Conv2d(1024, 512, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(512, 512, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(512, 2048, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(2048, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
        (downsample): Sequential(
          (0): Conv2d(1024, 2048, kernel_size=(1, 1), stride=(2, 2), bias=False)
          (1): BatchNorm2d(2048, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        )
      )
      (1): Bottleneck(
        (conv1): Conv2d(2048, 512, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(512, 2048, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(2048, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
      )
      (2): Bottleneck(
        (conv1): Conv2d(2048, 512, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(512, 2048, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(2048, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
      )
    )
  )
  init_cfg={'type': 'Pretrained', 'checkpoint': 'ckpt/resnet50-19c8e357.pth'}
  (img_neck): FPN(
    (lateral_convs): ModuleList(
      (0): ConvModule(
        (conv): Conv2d(256, 256, kernel_size=(1, 1), stride=(1, 1))
      )
      (1): ConvModule(
        (conv): Conv2d(512, 256, kernel_size=(1, 1), stride=(1, 1))
      )
      (2): ConvModule(
        (conv): Conv2d(1024, 256, kernel_size=(1, 1), stride=(1, 1))
      )
      (3): ConvModule(
        (conv): Conv2d(2048, 256, kernel_size=(1, 1), stride=(1, 1))
      )
    )
    (fpn_convs): ModuleList(
      (0): ConvModule(
        (conv): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
      )
      (1): ConvModule(
        (conv): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
      )
      (2): ConvModule(
        (conv): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
      )
      (3): ConvModule(
        (conv): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
      )
    )
  )
  init_cfg={'type': 'Xavier', 'layer': 'Conv2d', 'distribution': 'uniform'}
  (head): Sparse4DHead(
    (instance_bank): InstanceBank(
      (anchor_handler): SparseBox3DKeyPointsGenerator()
    )
    (anchor_encoder): SparseBox3DEncoder(
      (pos_fc): Sequential(
        (0): Linear(in_features=3, out_features=128, bias=True)
        (1): ReLU(inplace=True)
        (2): LayerNorm((128,), eps=1e-05, elementwise_affine=True)
        (3): Linear(in_features=128, out_features=128, bias=True)
        (4): ReLU(inplace=True)
        (5): LayerNorm((128,), eps=1e-05, elementwise_affine=True)
        (6): Linear(in_features=128, out_features=128, bias=True)
        (7): ReLU(inplace=True)
        (8): LayerNorm((128,), eps=1e-05, elementwise_affine=True)
        (9): Linear(in_features=128, out_features=128, bias=True)
        (10): ReLU(inplace=True)
        (11): LayerNorm((128,), eps=1e-05, elementwise_affine=True)
      )
      (size_fc): Sequential(
        (0): Linear(in_features=3, out_features=32, bias=True)
        (1): ReLU(inplace=True)
        (2): LayerNorm((32,), eps=1e-05, elementwise_affine=True)
        (3): Linear(in_features=32, out_features=32, bias=True)
        (4): ReLU(inplace=True)
        (5): LayerNorm((32,), eps=1e-05, elementwise_affine=True)
        (6): Linear(in_features=32, out_features=32, bias=True)
        (7): ReLU(inplace=True)
        (8): LayerNorm((32,), eps=1e-05, elementwise_affine=True)
        (9): Linear(in_features=32, out_features=32, bias=True)
        (10): ReLU(inplace=True)
        (11): LayerNorm((32,), eps=1e-05, elementwise_affine=True)
      )
      (yaw_fc): Sequential(
        (0): Linear(in_features=2, out_features=32, bias=True)
        (1): ReLU(inplace=True)
        (2): LayerNorm((32,), eps=1e-05, elementwise_affine=True)
        (3): Linear(in_features=32, out_features=32, bias=True)
        (4): ReLU(inplace=True)
        (5): LayerNorm((32,), eps=1e-05, elementwise_affine=True)
        (6): Linear(in_features=32, out_features=32, bias=True)
        (7): ReLU(inplace=True)
        (8): LayerNorm((32,), eps=1e-05, elementwise_affine=True)
        (9): Linear(in_features=32, out_features=32, bias=True)
        (10): ReLU(inplace=True)
        (11): LayerNorm((32,), eps=1e-05, elementwise_affine=True)
      )
      (vel_fc): Sequential(
        (0): Linear(in_features=3, out_features=64, bias=True)
        (1): ReLU(inplace=True)
        (2): LayerNorm((64,), eps=1e-05, elementwise_affine=True)
        (3): Linear(in_features=64, out_features=64, bias=True)
        (4): ReLU(inplace=True)
        (5): LayerNorm((64,), eps=1e-05, elementwise_affine=True)
        (6): Linear(in_features=64, out_features=64, bias=True)
        (7): ReLU(inplace=True)
        (8): LayerNorm((64,), eps=1e-05, elementwise_affine=True)
        (9): Linear(in_features=64, out_features=64, bias=True)
        (10): ReLU(inplace=True)
        (11): LayerNorm((64,), eps=1e-05, elementwise_affine=True)
      )
    )
    (loss_cls): FocalLoss()
    (loss_reg): SparseBox3DLoss(
      (loss_box): L1Loss()
      (loss_cns): CrossEntropyLoss(avg_non_ignore=False)
      (loss_yns): GaussianFocalLoss()
    )
    (layers): ModuleList(
      (0): DeformableFeatureAggregation(
        (proj_drop): Dropout(p=0.0, inplace=False)
        (kps_generator): SparseBox3DKeyPointsGenerator(
          (learnable_fc): Linear(in_features=256, out_features=18, bias=True)
        )
        (output_proj): Linear(in_features=256, out_features=256, bias=True)
        (camera_encoder): Sequential(
          (0): Linear(in_features=12, out_features=256, bias=True)
          (1): ReLU(inplace=True)
          (2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (3): Linear(in_features=256, out_features=256, bias=True)
          (4): ReLU(inplace=True)
          (5): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        )
        (weights_fc): Linear(in_features=256, out_features=416, bias=True)
      )
      (1): AsymmetricFFN(
        (activate): ReLU(inplace=True)
        (pre_norm): LayerNorm((512,), eps=1e-05, elementwise_affine=True)
        (layers): Sequential(
          (0): Sequential(
            (0): Linear(in_features=512, out_features=1024, bias=True)
            (1): ReLU(inplace=True)
            (2): Dropout(p=0.1, inplace=False)
          )
          (1): Linear(in_features=1024, out_features=256, bias=True)
          (2): Dropout(p=0.1, inplace=False)
        )
        (dropout_layer): Identity()
        (identity_fc): Linear(in_features=512, out_features=256, bias=True)
      )
      (2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
      (3): SparseBox3DRefinementModule(
        (layers): Sequential(
          (0): Linear(in_features=256, out_features=256, bias=True)
          (1): ReLU(inplace=True)
          (2): Linear(in_features=256, out_features=256, bias=True)
          (3): ReLU(inplace=True)
          (4): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (5): Linear(in_features=256, out_features=256, bias=True)
          (6): ReLU(inplace=True)
          (7): Linear(in_features=256, out_features=256, bias=True)
          (8): ReLU(inplace=True)
          (9): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (10): Linear(in_features=256, out_features=11, bias=True)
          (11): Scale()
        )
        (cls_layers): Sequential(
          (0): Linear(in_features=256, out_features=256, bias=True)
          (1): ReLU(inplace=True)
          (2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (3): Linear(in_features=256, out_features=256, bias=True)
          (4): ReLU(inplace=True)
          (5): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (6): Linear(in_features=256, out_features=10, bias=True)
        )
        (quality_layers): Sequential(
          (0): Linear(in_features=256, out_features=256, bias=True)
          (1): ReLU(inplace=True)
          (2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (3): Linear(in_features=256, out_features=256, bias=True)
          (4): ReLU(inplace=True)
          (5): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (6): Linear(in_features=256, out_features=2, bias=True)
        )
      )
      (4): MultiheadAttention(
        (attn): MultiheadAttention(
          (out_proj): NonDynamicallyQuantizableLinear(in_features=512, out_features=512, bias=True)
        )
        (proj_drop): Dropout(p=0.0, inplace=False)
        (dropout_layer): Dropout(p=0.1, inplace=False)
      )
      (5): MultiheadAttention(
        (attn): MultiheadAttention(
          (out_proj): NonDynamicallyQuantizableLinear(in_features=512, out_features=512, bias=True)
        )
        (proj_drop): Dropout(p=0.0, inplace=False)
        (dropout_layer): Dropout(p=0.1, inplace=False)
      )
      (6): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
      (7): DeformableFeatureAggregation(
        (proj_drop): Dropout(p=0.0, inplace=False)
        (kps_generator): SparseBox3DKeyPointsGenerator(
          (learnable_fc): Linear(in_features=256, out_features=18, bias=True)
        )
        (output_proj): Linear(in_features=256, out_features=256, bias=True)
        (camera_encoder): Sequential(
          (0): Linear(in_features=12, out_features=256, bias=True)
          (1): ReLU(inplace=True)
          (2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (3): Linear(in_features=256, out_features=256, bias=True)
          (4): ReLU(inplace=True)
          (5): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        )
        (weights_fc): Linear(in_features=256, out_features=416, bias=True)
      )
      (8): AsymmetricFFN(
        (activate): ReLU(inplace=True)
        (pre_norm): LayerNorm((512,), eps=1e-05, elementwise_affine=True)
        (layers): Sequential(
          (0): Sequential(
            (0): Linear(in_features=512, out_features=1024, bias=True)
            (1): ReLU(inplace=True)
            (2): Dropout(p=0.1, inplace=False)
          )
          (1): Linear(in_features=1024, out_features=256, bias=True)
          (2): Dropout(p=0.1, inplace=False)
        )
        (dropout_layer): Identity()
        (identity_fc): Linear(in_features=512, out_features=256, bias=True)
      )
      (9): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
      (10): SparseBox3DRefinementModule(
        (layers): Sequential(
          (0): Linear(in_features=256, out_features=256, bias=True)
          (1): ReLU(inplace=True)
          (2): Linear(in_features=256, out_features=256, bias=True)
          (3): ReLU(inplace=True)
          (4): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (5): Linear(in_features=256, out_features=256, bias=True)
          (6): ReLU(inplace=True)
          (7): Linear(in_features=256, out_features=256, bias=True)
          (8): ReLU(inplace=True)
          (9): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (10): Linear(in_features=256, out_features=11, bias=True)
          (11): Scale()
        )
        (cls_layers): Sequential(
          (0): Linear(in_features=256, out_features=256, bias=True)
          (1): ReLU(inplace=True)
          (2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (3): Linear(in_features=256, out_features=256, bias=True)
          (4): ReLU(inplace=True)
          (5): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (6): Linear(in_features=256, out_features=10, bias=True)
        )
        (quality_layers): Sequential(
          (0): Linear(in_features=256, out_features=256, bias=True)
          (1): ReLU(inplace=True)
          (2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (3): Linear(in_features=256, out_features=256, bias=True)
          (4): ReLU(inplace=True)
          (5): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (6): Linear(in_features=256, out_features=2, bias=True)
        )
      )
      (11): MultiheadAttention(
        (attn): MultiheadAttention(
          (out_proj): NonDynamicallyQuantizableLinear(in_features=512, out_features=512, bias=True)
        )
        (proj_drop): Dropout(p=0.0, inplace=False)
        (dropout_layer): Dropout(p=0.1, inplace=False)
      )
      (12): MultiheadAttention(
        (attn): MultiheadAttention(
          (out_proj): NonDynamicallyQuantizableLinear(in_features=512, out_features=512, bias=True)
        )
        (proj_drop): Dropout(p=0.0, inplace=False)
        (dropout_layer): Dropout(p=0.1, inplace=False)
      )
      (13): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
      (14): DeformableFeatureAggregation(
        (proj_drop): Dropout(p=0.0, inplace=False)
        (kps_generator): SparseBox3DKeyPointsGenerator(
          (learnable_fc): Linear(in_features=256, out_features=18, bias=True)
        )
        (output_proj): Linear(in_features=256, out_features=256, bias=True)
        (camera_encoder): Sequential(
          (0): Linear(in_features=12, out_features=256, bias=True)
          (1): ReLU(inplace=True)
          (2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (3): Linear(in_features=256, out_features=256, bias=True)
          (4): ReLU(inplace=True)
          (5): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        )
        (weights_fc): Linear(in_features=256, out_features=416, bias=True)
      )
      (15): AsymmetricFFN(
        (activate): ReLU(inplace=True)
        (pre_norm): LayerNorm((512,), eps=1e-05, elementwise_affine=True)
        (layers): Sequential(
          (0): Sequential(
            (0): Linear(in_features=512, out_features=1024, bias=True)
            (1): ReLU(inplace=True)
            (2): Dropout(p=0.1, inplace=False)
          )
          (1): Linear(in_features=1024, out_features=256, bias=True)
          (2): Dropout(p=0.1, inplace=False)
        )
        (dropout_layer): Identity()
        (identity_fc): Linear(in_features=512, out_features=256, bias=True)
      )
      (16): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
      (17): SparseBox3DRefinementModule(
        (layers): Sequential(
          (0): Linear(in_features=256, out_features=256, bias=True)
          (1): ReLU(inplace=True)
          (2): Linear(in_features=256, out_features=256, bias=True)
          (3): ReLU(inplace=True)
          (4): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (5): Linear(in_features=256, out_features=256, bias=True)
          (6): ReLU(inplace=True)
          (7): Linear(in_features=256, out_features=256, bias=True)
          (8): ReLU(inplace=True)
          (9): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (10): Linear(in_features=256, out_features=11, bias=True)
          (11): Scale()
        )
        (cls_layers): Sequential(
          (0): Linear(in_features=256, out_features=256, bias=True)
          (1): ReLU(inplace=True)
          (2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (3): Linear(in_features=256, out_features=256, bias=True)
          (4): ReLU(inplace=True)
          (5): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (6): Linear(in_features=256, out_features=10, bias=True)
        )
        (quality_layers): Sequential(
          (0): Linear(in_features=256, out_features=256, bias=True)
          (1): ReLU(inplace=True)
          (2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (3): Linear(in_features=256, out_features=256, bias=True)
          (4): ReLU(inplace=True)
          (5): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (6): Linear(in_features=256, out_features=2, bias=True)
        )
      )
      (18): MultiheadAttention(
        (attn): MultiheadAttention(
          (out_proj): NonDynamicallyQuantizableLinear(in_features=512, out_features=512, bias=True)
        )
        (proj_drop): Dropout(p=0.0, inplace=False)
        (dropout_layer): Dropout(p=0.1, inplace=False)
      )
      (19): MultiheadAttention(
        (attn): MultiheadAttention(
          (out_proj): NonDynamicallyQuantizableLinear(in_features=512, out_features=512, bias=True)
        )
        (proj_drop): Dropout(p=0.0, inplace=False)
        (dropout_layer): Dropout(p=0.1, inplace=False)
      )
      (20): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
      (21): DeformableFeatureAggregation(
        (proj_drop): Dropout(p=0.0, inplace=False)
        (kps_generator): SparseBox3DKeyPointsGenerator(
          (learnable_fc): Linear(in_features=256, out_features=18, bias=True)
        )
        (output_proj): Linear(in_features=256, out_features=256, bias=True)
        (camera_encoder): Sequential(
          (0): Linear(in_features=12, out_features=256, bias=True)
          (1): ReLU(inplace=True)
          (2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (3): Linear(in_features=256, out_features=256, bias=True)
          (4): ReLU(inplace=True)
          (5): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        )
        (weights_fc): Linear(in_features=256, out_features=416, bias=True)
      )
      (22): AsymmetricFFN(
        (activate): ReLU(inplace=True)
        (pre_norm): LayerNorm((512,), eps=1e-05, elementwise_affine=True)
        (layers): Sequential(
          (0): Sequential(
            (0): Linear(in_features=512, out_features=1024, bias=True)
            (1): ReLU(inplace=True)
            (2): Dropout(p=0.1, inplace=False)
          )
          (1): Linear(in_features=1024, out_features=256, bias=True)
          (2): Dropout(p=0.1, inplace=False)
        )
        (dropout_layer): Identity()
        (identity_fc): Linear(in_features=512, out_features=256, bias=True)
      )
      (23): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
      (24): SparseBox3DRefinementModule(
        (layers): Sequential(
          (0): Linear(in_features=256, out_features=256, bias=True)
          (1): ReLU(inplace=True)
          (2): Linear(in_features=256, out_features=256, bias=True)
          (3): ReLU(inplace=True)
          (4): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (5): Linear(in_features=256, out_features=256, bias=True)
          (6): ReLU(inplace=True)
          (7): Linear(in_features=256, out_features=256, bias=True)
          (8): ReLU(inplace=True)
          (9): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (10): Linear(in_features=256, out_features=11, bias=True)
          (11): Scale()
        )
        (cls_layers): Sequential(
          (0): Linear(in_features=256, out_features=256, bias=True)
          (1): ReLU(inplace=True)
          (2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (3): Linear(in_features=256, out_features=256, bias=True)
          (4): ReLU(inplace=True)
          (5): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (6): Linear(in_features=256, out_features=10, bias=True)
        )
        (quality_layers): Sequential(
          (0): Linear(in_features=256, out_features=256, bias=True)
          (1): ReLU(inplace=True)
          (2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (3): Linear(in_features=256, out_features=256, bias=True)
          (4): ReLU(inplace=True)
          (5): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (6): Linear(in_features=256, out_features=2, bias=True)
        )
      )
      (25): MultiheadAttention(
        (attn): MultiheadAttention(
          (out_proj): NonDynamicallyQuantizableLinear(in_features=512, out_features=512, bias=True)
        )
        (proj_drop): Dropout(p=0.0, inplace=False)
        (dropout_layer): Dropout(p=0.1, inplace=False)
      )
      (26): MultiheadAttention(
        (attn): MultiheadAttention(
          (out_proj): NonDynamicallyQuantizableLinear(in_features=512, out_features=512, bias=True)
        )
        (proj_drop): Dropout(p=0.0, inplace=False)
        (dropout_layer): Dropout(p=0.1, inplace=False)
      )
      (27): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
      (28): DeformableFeatureAggregation(
        (proj_drop): Dropout(p=0.0, inplace=False)
        (kps_generator): SparseBox3DKeyPointsGenerator(
          (learnable_fc): Linear(in_features=256, out_features=18, bias=True)
        )
        (output_proj): Linear(in_features=256, out_features=256, bias=True)
        (camera_encoder): Sequential(
          (0): Linear(in_features=12, out_features=256, bias=True)
          (1): ReLU(inplace=True)
          (2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (3): Linear(in_features=256, out_features=256, bias=True)
          (4): ReLU(inplace=True)
          (5): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        )
        (weights_fc): Linear(in_features=256, out_features=416, bias=True)
      )
      (29): AsymmetricFFN(
        (activate): ReLU(inplace=True)
        (pre_norm): LayerNorm((512,), eps=1e-05, elementwise_affine=True)
        (layers): Sequential(
          (0): Sequential(
            (0): Linear(in_features=512, out_features=1024, bias=True)
            (1): ReLU(inplace=True)
            (2): Dropout(p=0.1, inplace=False)
          )
          (1): Linear(in_features=1024, out_features=256, bias=True)
          (2): Dropout(p=0.1, inplace=False)
        )
        (dropout_layer): Identity()
        (identity_fc): Linear(in_features=512, out_features=256, bias=True)
      )
      (30): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
      (31): SparseBox3DRefinementModule(
        (layers): Sequential(
          (0): Linear(in_features=256, out_features=256, bias=True)
          (1): ReLU(inplace=True)
          (2): Linear(in_features=256, out_features=256, bias=True)
          (3): ReLU(inplace=True)
          (4): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (5): Linear(in_features=256, out_features=256, bias=True)
          (6): ReLU(inplace=True)
          (7): Linear(in_features=256, out_features=256, bias=True)
          (8): ReLU(inplace=True)
          (9): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (10): Linear(in_features=256, out_features=11, bias=True)
          (11): Scale()
        )
        (cls_layers): Sequential(
          (0): Linear(in_features=256, out_features=256, bias=True)
          (1): ReLU(inplace=True)
          (2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (3): Linear(in_features=256, out_features=256, bias=True)
          (4): ReLU(inplace=True)
          (5): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (6): Linear(in_features=256, out_features=10, bias=True)
        )
        (quality_layers): Sequential(
          (0): Linear(in_features=256, out_features=256, bias=True)
          (1): ReLU(inplace=True)
          (2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (3): Linear(in_features=256, out_features=256, bias=True)
          (4): ReLU(inplace=True)
          (5): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (6): Linear(in_features=256, out_features=2, bias=True)
        )
      )
      (32): MultiheadAttention(
        (attn): MultiheadAttention(
          (out_proj): NonDynamicallyQuantizableLinear(in_features=512, out_features=512, bias=True)
        )
        (proj_drop): Dropout(p=0.0, inplace=False)
        (dropout_layer): Dropout(p=0.1, inplace=False)
      )
      (33): MultiheadAttention(
        (attn): MultiheadAttention(
          (out_proj): NonDynamicallyQuantizableLinear(in_features=512, out_features=512, bias=True)
        )
        (proj_drop): Dropout(p=0.0, inplace=False)
        (dropout_layer): Dropout(p=0.1, inplace=False)
      )
      (34): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
      (35): DeformableFeatureAggregation(
        (proj_drop): Dropout(p=0.0, inplace=False)
        (kps_generator): SparseBox3DKeyPointsGenerator(
          (learnable_fc): Linear(in_features=256, out_features=18, bias=True)
        )
        (output_proj): Linear(in_features=256, out_features=256, bias=True)
        (camera_encoder): Sequential(
          (0): Linear(in_features=12, out_features=256, bias=True)
          (1): ReLU(inplace=True)
          (2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (3): Linear(in_features=256, out_features=256, bias=True)
          (4): ReLU(inplace=True)
          (5): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        )
        (weights_fc): Linear(in_features=256, out_features=416, bias=True)
      )
      (36): AsymmetricFFN(
        (activate): ReLU(inplace=True)
        (pre_norm): LayerNorm((512,), eps=1e-05, elementwise_affine=True)
        (layers): Sequential(
          (0): Sequential(
            (0): Linear(in_features=512, out_features=1024, bias=True)
            (1): ReLU(inplace=True)
            (2): Dropout(p=0.1, inplace=False)
          )
          (1): Linear(in_features=1024, out_features=256, bias=True)
          (2): Dropout(p=0.1, inplace=False)
        )
        (dropout_layer): Identity()
        (identity_fc): Linear(in_features=512, out_features=256, bias=True)
      )
      (37): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
      (38): SparseBox3DRefinementModule(
        (layers): Sequential(
          (0): Linear(in_features=256, out_features=256, bias=True)
          (1): ReLU(inplace=True)
          (2): Linear(in_features=256, out_features=256, bias=True)
          (3): ReLU(inplace=True)
          (4): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (5): Linear(in_features=256, out_features=256, bias=True)
          (6): ReLU(inplace=True)
          (7): Linear(in_features=256, out_features=256, bias=True)
          (8): ReLU(inplace=True)
          (9): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (10): Linear(in_features=256, out_features=11, bias=True)
          (11): Scale()
        )
        (cls_layers): Sequential(
          (0): Linear(in_features=256, out_features=256, bias=True)
          (1): ReLU(inplace=True)
          (2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (3): Linear(in_features=256, out_features=256, bias=True)
          (4): ReLU(inplace=True)
          (5): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (6): Linear(in_features=256, out_features=10, bias=True)
        )
        (quality_layers): Sequential(
          (0): Linear(in_features=256, out_features=256, bias=True)
          (1): ReLU(inplace=True)
          (2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (3): Linear(in_features=256, out_features=256, bias=True)
          (4): ReLU(inplace=True)
          (5): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (6): Linear(in_features=256, out_features=2, bias=True)
        )
      )
    )
    (fc_before): Linear(in_features=256, out_features=512, bias=False)
    (fc_after): Linear(in_features=512, out_features=256, bias=False)
  )
  (depth_branch): DenseDepthNet(
    (depth_layers): ModuleList(
      (0): Conv2d(256, 1, kernel_size=(1, 1), stride=(1, 1))
      (1): Conv2d(256, 1, kernel_size=(1, 1), stride=(1, 1))
      (2): Conv2d(256, 1, kernel_size=(1, 1), stride=(1, 1))
    )
  )
  (grid_mask): GridMask()
)
2025-06-25 09:53:57,288 - mmdet - INFO - Start running, host: root@lsq-Precision-3680, work_dir: /workspace/py-develop/Sparse4D/work_dirs/sparse4dv3_temporal_r50_1x8_bs6_256x704
2025-06-25 09:53:57,289 - mmdet - INFO - Hooks will be executed in the following order:
before_run:
(VERY_HIGH   ) CosineAnnealingLrUpdaterHook       
(ABOVE_NORMAL) Fp16OptimizerHook                  
(NORMAL      ) CheckpointHook                     
(NORMAL      ) EvalHook                           
(VERY_LOW    ) TextLoggerHook                     
(VERY_LOW    ) TensorboardLoggerHook              
 -------------------- 
before_train_epoch:
(VERY_HIGH   ) CosineAnnealingLrUpdaterHook       
(NORMAL      ) EvalHook                           
(LOW         ) IterTimerHook                      
(VERY_LOW    ) TextLoggerHook                     
(VERY_LOW    ) TensorboardLoggerHook              
 -------------------- 
before_train_iter:
(VERY_HIGH   ) CosineAnnealingLrUpdaterHook       
(NORMAL      ) EvalHook                           
(LOW         ) IterTimerHook                      
 -------------------- 
after_train_iter:
(ABOVE_NORMAL) Fp16OptimizerHook                  
(NORMAL      ) CheckpointHook                     
(NORMAL      ) EvalHook                           
(LOW         ) IterTimerHook                      
(VERY_LOW    ) TextLoggerHook                     
(VERY_LOW    ) TensorboardLoggerHook              
 -------------------- 
after_train_epoch:
(NORMAL      ) CheckpointHook                     
(NORMAL      ) EvalHook                           
(VERY_LOW    ) TextLoggerHook                     
(VERY_LOW    ) TensorboardLoggerHook              
 -------------------- 
before_val_epoch:
(LOW         ) IterTimerHook                      
(VERY_LOW    ) TextLoggerHook                     
(VERY_LOW    ) TensorboardLoggerHook              
 -------------------- 
before_val_iter:
(LOW         ) IterTimerHook                      
 -------------------- 
after_val_iter:
(LOW         ) IterTimerHook                      
 -------------------- 
after_val_epoch:
(VERY_LOW    ) TextLoggerHook                     
(VERY_LOW    ) TensorboardLoggerHook              
 -------------------- 
after_run:
(VERY_LOW    ) TextLoggerHook                     
(VERY_LOW    ) TensorboardLoggerHook              
 -------------------- 
2025-06-25 09:53:57,289 - mmdet - INFO - workflow: [('train', 1)], max: 58600 iters
2025-06-25 09:53:57,291 - mmdet - INFO - Checkpoints will be saved to /workspace/py-develop/Sparse4D/work_dirs/sparse4dv3_temporal_r50_1x8_bs6_256x704 by HardDiskBackend.
