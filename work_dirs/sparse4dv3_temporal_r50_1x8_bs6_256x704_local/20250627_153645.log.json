{"env_info": "sys.platform: linux\nPython: 3.10.18 (main, Jun  5 2025, 13:14:17) [GCC 11.2.0]\nCUDA available: True\nGPU 0: NVIDIA RTX 2000 Ada Generation\nCUDA_HOME: /usr/local/cuda\nNVCC: Cuda compilation tools, release 11.6, V11.6.124\nGCC: gcc (Ubuntu 9.4.0-1ubuntu1~20.04.2) 9.4.0\nPyTorch: 1.13.0+cu116\nPyTorch compiling details: PyTorch built with:\n  - GCC 9.3\n  - C++ Version: 201402\n  - Intel(R) Math Kernel Library Version 2020.0.0 Product Build 20191122 for Intel(R) 64 architecture applications\n  - Intel(R) MKL-DNN v2.6.0 (Git Hash 52b5f107dd9cf10910aaa19cb47f3abf9b349815)\n  - OpenMP 201511 (a.k.a. OpenMP 4.5)\n  - LAPACK is enabled (usually provided by MKL)\n  - NNPACK is enabled\n  - CPU capability usage: AVX2\n  - CUDA Runtime 11.6\n  - NVCC architecture flags: -gencode;arch=compute_37,code=sm_37;-gencode;arch=compute_50,code=sm_50;-gencode;arch=compute_60,code=sm_60;-gencode;arch=compute_70,code=sm_70;-gencode;arch=compute_75,code=sm_75;-gencode;arch=compute_80,code=sm_80;-gencode;arch=compute_86,code=sm_86\n  - CuDNN 8.3.2  (built against CUDA 11.5)\n  - Magma 2.6.1\n  - Build settings: BLAS_INFO=mkl, BUILD_TYPE=Release, CUDA_VERSION=11.6, CUDNN_VERSION=8.3.2, CXX_COMPILER=/opt/rh/devtoolset-9/root/usr/bin/c++, CXX_FLAGS= -fabi-version=11 -Wno-deprecated -fvisibility-inlines-hidden -DUSE_PTHREADPOOL -fopenmp -DNDEBUG -DUSE_KINETO -DUSE_FBGEMM -DUSE_QNNPACK -DUSE_PYTORCH_QNNPACK -DUSE_XNNPACK -DSYMBOLICATE_MOBILE_DEBUG_HANDLE -DEDGE_PROFILER_USE_KINETO -O2 -fPIC -Wno-narrowing -Wall -Wextra -Werror=return-type -Werror=non-virtual-dtor -Wno-missing-field-initializers -Wno-type-limits -Wno-array-bounds -Wno-unknown-pragmas -Wunused-local-typedefs -Wno-unused-parameter -Wno-unused-function -Wno-unused-result -Wno-strict-overflow -Wno-strict-aliasing -Wno-error=deprecated-declarations -Wno-stringop-overflow -Wno-psabi -Wno-error=pedantic -Wno-error=redundant-decls -Wno-error=old-style-cast -fdiagnostics-color=always -faligned-new -Wno-unused-but-set-variable -Wno-maybe-uninitialized -fno-math-errno -fno-trapping-math -Werror=format -Werror=cast-function-type -Wno-stringop-overflow, LAPACK_INFO=mkl, PERF_WITH_AVX=1, PERF_WITH_AVX2=1, PERF_WITH_AVX512=1, TORCH_VERSION=1.13.0, USE_CUDA=ON, USE_CUDNN=ON, USE_EXCEPTION_PTR=1, USE_GFLAGS=OFF, USE_GLOG=OFF, USE_MKL=ON, USE_MKLDNN=ON, USE_MPI=OFF, USE_NCCL=ON, USE_NNPACK=ON, USE_OPENMP=ON, USE_ROCM=OFF, \n\nTorchVision: 0.14.0+cu116\nOpenCV: 4.11.0\nMMCV: 1.7.1\nMMCV Compiler: GCC 9.4\nMMCV CUDA Compiler: 11.6\nMMDetection: 2.28.2+unknown", "config": "plugin = True\nplugin_dir = 'projects/mmdet3d_plugin/'\ndist_params = dict(backend='nccl')\nlog_level = 'INFO'\nwork_dir = './work_dirs/sparse4dv3_temporal_r50_1x8_bs6_256x704_local'\ntotal_batch_size = 1\nnum_gpus = 1\nbatch_size = 1\nnum_iters_per_epoch = 28130\nnum_epochs = 100\ncheckpoint_epoch_interval = 20\ncheckpoint_config = dict(interval=562600)\nlog_config = dict(\n    interval=51,\n    hooks=[\n        dict(type='TextLoggerHook', by_epoch=False),\n        dict(type='TensorboardLoggerHook')\n    ])\nload_from = None\nresume_from = None\nworkflow = [('train', 1)]\nfp16 = dict(loss_scale=32.0)\ninput_shape = (704, 256)\ntracking_test = True\ntracking_threshold = 0.2\nclass_names = [\n    'car', 'truck', 'construction_vehicle', 'bus', 'trailer', 'barrier',\n    'motorcycle', 'bicycle', 'pedestrian', 'traffic_cone'\n]\nnum_classes = 10\nembed_dims = 256\nnum_groups = 8\nnum_decoder = 6\nnum_single_frame_decoder = 1\nuse_deformable_func = True\nstrides = [4, 8, 16, 32]\nnum_levels = 4\nnum_depth_layers = 3\ndrop_out = 0.1\ntemporal = True\ndecouple_attn = True\nwith_quality_estimation = True\nmodel = dict(\n    type='Sparse4D',\n    use_grid_mask=True,\n    use_deformable_func=True,\n    img_backbone=dict(\n        type='ResNet',\n        depth=50,\n        num_stages=4,\n        frozen_stages=-1,\n        norm_eval=False,\n        style='pytorch',\n        with_cp=True,\n        out_indices=(0, 1, 2, 3),\n        norm_cfg=dict(type='BN', requires_grad=True),\n        pretrained='ckpt/resnet50-19c8e357.pth'),\n    img_neck=dict(\n        type='FPN',\n        num_outs=4,\n        start_level=0,\n        out_channels=256,\n        add_extra_convs='on_output',\n        relu_before_extra_convs=True,\n        in_channels=[256, 512, 1024, 2048]),\n    depth_branch=dict(\n        type='DenseDepthNet',\n        embed_dims=256,\n        num_depth_layers=3,\n        loss_weight=0.2),\n    head=dict(\n        type='Sparse4DHead',\n        cls_threshold_to_reg=0.05,\n        decouple_attn=True,\n        instance_bank=dict(\n            type='InstanceBank',\n            num_anchor=900,\n            embed_dims=256,\n            anchor='nuscenes_kmeans900.npy',\n            anchor_handler=dict(type='SparseBox3DKeyPointsGenerator'),\n            num_temp_instances=600,\n            confidence_decay=0.6,\n            feat_grad=False),\n        anchor_encoder=dict(\n            type='SparseBox3DEncoder',\n            vel_dims=3,\n            embed_dims=[128, 32, 32, 64],\n            mode='cat',\n            output_fc=False,\n            in_loops=1,\n            out_loops=4),\n        num_single_frame_decoder=1,\n        operation_order=[\n            'deformable', 'ffn', 'norm', 'refine', 'temp_gnn', 'gnn', 'norm',\n            'deformable', 'ffn', 'norm', 'refine', 'temp_gnn', 'gnn', 'norm',\n            'deformable', 'ffn', 'norm', 'refine', 'temp_gnn', 'gnn', 'norm',\n            'deformable', 'ffn', 'norm', 'refine', 'temp_gnn', 'gnn', 'norm',\n            'deformable', 'ffn', 'norm', 'refine', 'temp_gnn', 'gnn', 'norm',\n            'deformable', 'ffn', 'norm', 'refine'\n        ],\n        temp_graph_model=dict(\n            type='MultiheadAttention',\n            embed_dims=512,\n            num_heads=8,\n            batch_first=True,\n            dropout=0.1),\n        graph_model=dict(\n            type='MultiheadAttention',\n            embed_dims=512,\n            num_heads=8,\n            batch_first=True,\n            dropout=0.1),\n        norm_layer=dict(type='LN', normalized_shape=256),\n        ffn=dict(\n            type='AsymmetricFFN',\n            in_channels=512,\n            pre_norm=dict(type='LN'),\n            embed_dims=256,\n            feedforward_channels=1024,\n            num_fcs=2,\n            ffn_drop=0.1,\n            act_cfg=dict(type='ReLU', inplace=True)),\n        deformable_model=dict(\n            type='DeformableFeatureAggregation',\n            embed_dims=256,\n            num_groups=8,\n            num_levels=4,\n            num_cams=6,\n            attn_drop=0.15,\n            use_deformable_func=True,\n            use_camera_embed=True,\n            residual_mode='cat',\n            kps_generator=dict(\n                type='SparseBox3DKeyPointsGenerator',\n                num_learnable_pts=6,\n                fix_scale=[[0, 0, 0], [0.45, 0, 0], [-0.45, 0, 0],\n                           [0, 0.45, 0], [0, -0.45, 0], [0, 0, 0.45],\n                           [0, 0, -0.45]])),\n        refine_layer=dict(\n            type='SparseBox3DRefinementModule',\n            embed_dims=256,\n            num_cls=10,\n            refine_yaw=True,\n            with_quality_estimation=True),\n        sampler=dict(\n            type='SparseBox3DTarget',\n            num_dn_groups=5,\n            num_temp_dn_groups=3,\n            dn_noise_scale=[2.0, 2.0, 2.0, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5],\n            max_dn_gt=32,\n            add_neg_dn=True,\n            cls_weight=2.0,\n            box_weight=0.25,\n            reg_weights=[2.0, 2.0, 2.0, 0.5, 0.5, 0.5, 0.0, 0.0, 0.0, 0.0],\n            cls_wise_reg_weights=dict(\n                {9: [2.0, 2.0, 2.0, 1.0, 1.0, 1.0, 0.0, 0.0, 1.0, 1.0]})),\n        loss_cls=dict(\n            type='FocalLoss',\n            use_sigmoid=True,\n            gamma=2.0,\n            alpha=0.25,\n            loss_weight=2.0),\n        loss_reg=dict(\n            type='SparseBox3DLoss',\n            loss_box=dict(type='L1Loss', loss_weight=0.25),\n            loss_centerness=dict(type='CrossEntropyLoss', use_sigmoid=True),\n            loss_yawness=dict(type='GaussianFocalLoss'),\n            cls_allow_reverse=[5]),\n        decoder=dict(type='SparseBox3DDecoder'),\n        reg_weights=[2.0, 2.0, 2.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0]))\ndataset_type = 'NuScenes3DDetTrackDataset'\ndata_root = 'data/nuscenes/'\nanno_root = 'data/nuscenes_anno_pkls/'\nfile_client_args = dict(backend='disk')\nimg_norm_cfg = dict(\n    mean=[123.675, 116.28, 103.53], std=[58.395, 57.12, 57.375], to_rgb=True)\ntrain_pipeline = [\n    dict(type='LoadMultiViewImageFromFiles', to_float32=True),\n    dict(\n        type='LoadPointsFromFile',\n        coord_type='LIDAR',\n        load_dim=5,\n        use_dim=5,\n        file_client_args=dict(backend='disk')),\n    dict(type='ResizeCropFlipImage'),\n    dict(type='MultiScaleDepthMapGenerator', downsample=[4, 8, 16]),\n    dict(type='BBoxRotation'),\n    dict(type='PhotoMetricDistortionMultiViewImage'),\n    dict(\n        type='NormalizeMultiviewImage',\n        mean=[123.675, 116.28, 103.53],\n        std=[58.395, 57.12, 57.375],\n        to_rgb=True),\n    dict(\n        type='CircleObjectRangeFilter',\n        class_dist_thred=[55, 55, 55, 55, 55, 55, 55, 55, 55, 55]),\n    dict(\n        type='InstanceNameFilter',\n        classes=[\n            'car', 'truck', 'construction_vehicle', 'bus', 'trailer',\n            'barrier', 'motorcycle', 'bicycle', 'pedestrian', 'traffic_cone'\n        ]),\n    dict(type='NuScenesSparse4DAdaptor'),\n    dict(\n        type='Collect',\n        keys=[\n            'img', 'timestamp', 'projection_mat', 'image_wh', 'gt_depth',\n            'focal', 'gt_bboxes_3d', 'gt_labels_3d'\n        ],\n        meta_keys=['T_global', 'T_global_inv', 'timestamp', 'instance_id'])\n]\ntest_pipeline = [\n    dict(type='LoadMultiViewImageFromFiles', to_float32=True),\n    dict(type='ResizeCropFlipImage'),\n    dict(\n        type='NormalizeMultiviewImage',\n        mean=[123.675, 116.28, 103.53],\n        std=[58.395, 57.12, 57.375],\n        to_rgb=True),\n    dict(type='NuScenesSparse4DAdaptor'),\n    dict(\n        type='Collect',\n        keys=['img', 'timestamp', 'projection_mat', 'image_wh'],\n        meta_keys=['T_global', 'T_global_inv', 'timestamp'])\n]\ninput_modality = dict(\n    use_lidar=False,\n    use_camera=True,\n    use_radar=False,\n    use_map=False,\n    use_external=False)\ndata_basic_config = dict(\n    type='NuScenes3DDetTrackDataset',\n    data_root='data/nuscenes/',\n    classes=[\n        'car', 'truck', 'construction_vehicle', 'bus', 'trailer', 'barrier',\n        'motorcycle', 'bicycle', 'pedestrian', 'traffic_cone'\n    ],\n    modality=dict(\n        use_lidar=False,\n        use_camera=True,\n        use_radar=False,\n        use_map=False,\n        use_external=False),\n    version='v1.0-mini')\ndata_aug_conf = dict(\n    resize_lim=(0.4, 0.47),\n    final_dim=(256, 704),\n    bot_pct_lim=(0.0, 0.0),\n    rot_lim=(-5.4, 5.4),\n    H=900,\n    W=1600,\n    rand_flip=True,\n    rot3d_range=[-0.3925, 0.3925])\ndata = dict(\n    samples_per_gpu=1,\n    workers_per_gpu=1,\n    train=dict(\n        type='NuScenes3DDetTrackDataset',\n        data_root='data/nuscenes/',\n        classes=[\n            'car', 'truck', 'construction_vehicle', 'bus', 'trailer',\n            'barrier', 'motorcycle', 'bicycle', 'pedestrian', 'traffic_cone'\n        ],\n        modality=dict(\n            use_lidar=False,\n            use_camera=True,\n            use_radar=False,\n            use_map=False,\n            use_external=False),\n        version='v1.0-mini',\n        ann_file='data/nuscenes_anno_pkls/nuscenes-mini_infos_train.pkl',\n        pipeline=[\n            dict(type='LoadMultiViewImageFromFiles', to_float32=True),\n            dict(\n                type='LoadPointsFromFile',\n                coord_type='LIDAR',\n                load_dim=5,\n                use_dim=5,\n                file_client_args=dict(backend='disk')),\n            dict(type='ResizeCropFlipImage'),\n            dict(type='MultiScaleDepthMapGenerator', downsample=[4, 8, 16]),\n            dict(type='BBoxRotation'),\n            dict(type='PhotoMetricDistortionMultiViewImage'),\n            dict(\n                type='NormalizeMultiviewImage',\n                mean=[123.675, 116.28, 103.53],\n                std=[58.395, 57.12, 57.375],\n                to_rgb=True),\n            dict(\n                type='CircleObjectRangeFilter',\n                class_dist_thred=[55, 55, 55, 55, 55, 55, 55, 55, 55, 55]),\n            dict(\n                type='InstanceNameFilter',\n                classes=[\n                    'car', 'truck', 'construction_vehicle', 'bus', 'trailer',\n                    'barrier', 'motorcycle', 'bicycle', 'pedestrian',\n                    'traffic_cone'\n                ]),\n            dict(type='NuScenesSparse4DAdaptor'),\n            dict(\n                type='Collect',\n                keys=[\n                    'img', 'timestamp', 'projection_mat', 'image_wh',\n                    'gt_depth', 'focal', 'gt_bboxes_3d', 'gt_labels_3d'\n                ],\n                meta_keys=[\n                    'T_global', 'T_global_inv', 'timestamp', 'instance_id'\n                ])\n        ],\n        test_mode=False,\n        data_aug_conf=dict(\n            resize_lim=(0.4, 0.47),\n            final_dim=(256, 704),\n            bot_pct_lim=(0.0, 0.0),\n            rot_lim=(-5.4, 5.4),\n            H=900,\n            W=1600,\n            rand_flip=True,\n            rot3d_range=[-0.3925, 0.3925]),\n        with_seq_flag=True,\n        sequences_split_num=2,\n        keep_consistent_seq_aug=True),\n    val=dict(\n        type='NuScenes3DDetTrackDataset',\n        data_root='data/nuscenes/',\n        classes=[\n            'car', 'truck', 'construction_vehicle', 'bus', 'trailer',\n            'barrier', 'motorcycle', 'bicycle', 'pedestrian', 'traffic_cone'\n        ],\n        modality=dict(\n            use_lidar=False,\n            use_camera=True,\n            use_radar=False,\n            use_map=False,\n            use_external=False),\n        version='v1.0-mini',\n        ann_file='data/nuscenes_anno_pkls/nuscenes-mini_infos_val.pkl',\n        pipeline=[\n            dict(type='LoadMultiViewImageFromFiles', to_float32=True),\n            dict(type='ResizeCropFlipImage'),\n            dict(\n                type='NormalizeMultiviewImage',\n                mean=[123.675, 116.28, 103.53],\n                std=[58.395, 57.12, 57.375],\n                to_rgb=True),\n            dict(type='NuScenesSparse4DAdaptor'),\n            dict(\n                type='Collect',\n                keys=['img', 'timestamp', 'projection_mat', 'image_wh'],\n                meta_keys=['T_global', 'T_global_inv', 'timestamp'])\n        ],\n        data_aug_conf=dict(\n            resize_lim=(0.4, 0.47),\n            final_dim=(256, 704),\n            bot_pct_lim=(0.0, 0.0),\n            rot_lim=(-5.4, 5.4),\n            H=900,\n            W=1600,\n            rand_flip=True,\n            rot3d_range=[-0.3925, 0.3925]),\n        test_mode=True,\n        tracking=True,\n        tracking_threshold=0.2),\n    test=dict(\n        type='NuScenes3DDetTrackDataset',\n        data_root='data/nuscenes/',\n        classes=[\n            'car', 'truck', 'construction_vehicle', 'bus', 'trailer',\n            'barrier', 'motorcycle', 'bicycle', 'pedestrian', 'traffic_cone'\n        ],\n        modality=dict(\n            use_lidar=False,\n            use_camera=True,\n            use_radar=False,\n            use_map=False,\n            use_external=False),\n        version='v1.0-mini',\n        ann_file='data/nuscenes_anno_pkls/nuscenes-mini_infos_val.pkl',\n        pipeline=[\n            dict(type='LoadMultiViewImageFromFiles', to_float32=True),\n            dict(type='ResizeCropFlipImage'),\n            dict(\n                type='NormalizeMultiviewImage',\n                mean=[123.675, 116.28, 103.53],\n                std=[58.395, 57.12, 57.375],\n                to_rgb=True),\n            dict(type='NuScenesSparse4DAdaptor'),\n            dict(\n                type='Collect',\n                keys=['img', 'timestamp', 'projection_mat', 'image_wh'],\n                meta_keys=['T_global', 'T_global_inv', 'timestamp'])\n        ],\n        data_aug_conf=dict(\n            resize_lim=(0.4, 0.47),\n            final_dim=(256, 704),\n            bot_pct_lim=(0.0, 0.0),\n            rot_lim=(-5.4, 5.4),\n            H=900,\n            W=1600,\n            rand_flip=True,\n            rot3d_range=[-0.3925, 0.3925]),\n        test_mode=True,\n        tracking=True,\n        tracking_threshold=0.2))\noptimizer = dict(\n    type='AdamW',\n    lr=0.0006,\n    weight_decay=0.001,\n    paramwise_cfg=dict(custom_keys=dict(img_backbone=dict(lr_mult=0.5))))\noptimizer_config = dict(grad_clip=dict(max_norm=25, norm_type=2))\nlr_config = dict(\n    policy='CosineAnnealing',\n    warmup='linear',\n    warmup_iters=500,\n    warmup_ratio=0.3333333333333333,\n    min_lr_ratio=0.001)\nrunner = dict(type='IterBasedRunner', max_iters=2813000)\nvis_pipeline = [\n    dict(type='LoadMultiViewImageFromFiles', to_float32=True),\n    dict(type='Collect', keys=['img'], meta_keys=['timestamp', 'lidar2img'])\n]\nevaluation = dict(\n    interval=562600,\n    pipeline=[\n        dict(type='LoadMultiViewImageFromFiles', to_float32=True),\n        dict(\n            type='Collect', keys=['img'], meta_keys=['timestamp', 'lidar2img'])\n    ])\ngpu_ids = range(0, 1)\n", "seed": 0, "exp_name": "sparse4dv3_temporal_r50_1x8_bs6_256x704_local.py"}
{"mode": "train", "epoch": 1, "iter": 51, "lr": 0.00012, "memory": 2847, "data_time": 0.00884, "loss_cls_0": 1.33522, "loss_box_0": 3.09647, "loss_cns_0": 0.58672, "loss_yns_0": 0.21687, "loss_cls_1": 1.3614, "loss_box_1": 3.36105, "loss_cns_1": 0.54584, "loss_yns_1": 0.16683, "loss_cls_2": 1.40091, "loss_box_2": 3.35705, "loss_cns_2": 0.53957, "loss_yns_2": 0.19031, "loss_cls_3": 1.36362, "loss_box_3": 3.50649, "loss_cns_3": 0.55856, "loss_yns_3": 0.18908, "loss_cls_4": 1.30445, "loss_box_4": 3.59421, "loss_cns_4": 0.55116, "loss_yns_4": 0.1909, "loss_cls_5": 1.30152, "loss_box_5": 3.68887, "loss_cns_5": 0.53202, "loss_yns_5": 0.2045, "loss_cls_dn_0": 0.5582, "loss_box_dn_0": 1.24083, "loss_cls_dn_1": 0.52355, "loss_box_dn_1": 3.18794, "loss_cls_dn_2": 0.53773, "loss_box_dn_2": 3.20588, "loss_cls_dn_3": 0.52044, "loss_box_dn_3": 3.27251, "loss_cls_dn_4": 0.49247, "loss_box_dn_4": 3.35965, "loss_cls_dn_5": 0.49866, "loss_box_dn_5": 3.45542, "loss_dense_depth": 1.36566, "loss": 55.36256, "grad_norm": 138.26942, "time": 0.57066}
{"mode": "train", "epoch": 1, "iter": 102, "lr": 0.00014, "memory": 2847, "data_time": 0.00397, "loss_cls_0": 1.39615, "loss_box_0": 2.99585, "loss_cns_0": 0.62755, "loss_yns_0": 0.1974, "loss_cls_1": 1.3112, "loss_box_1": 3.73664, "loss_cns_1": 0.52101, "loss_yns_1": 0.17429, "loss_cls_2": 1.31965, "loss_box_2": 3.62908, "loss_cns_2": 0.53444, "loss_yns_2": 0.16628, "loss_cls_3": 1.35035, "loss_box_3": 3.66757, "loss_cns_3": 0.5397, "loss_yns_3": 0.17409, "loss_cls_4": 1.39059, "loss_box_4": 3.69226, "loss_cns_4": 0.54286, "loss_yns_4": 0.16745, "loss_cls_5": 1.35055, "loss_box_5": 3.76353, "loss_cns_5": 0.54769, "loss_yns_5": 0.17724, "loss_cls_dn_0": 0.54866, "loss_box_dn_0": 1.03531, "loss_cls_dn_1": 0.43664, "loss_box_dn_1": 1.96454, "loss_cls_dn_2": 0.46171, "loss_box_dn_2": 1.92122, "loss_cls_dn_3": 0.46276, "loss_box_dn_3": 1.95812, "loss_cls_dn_4": 0.4705, "loss_box_dn_4": 2.00074, "loss_cls_dn_5": 0.44452, "loss_box_dn_5": 2.08831, "loss_dense_depth": 1.34262, "loss": 49.10906, "grad_norm": 106.09737, "time": 0.54067}
