{"env_info": "sys.platform: linux\nPython: 3.10.18 (main, Jun  5 2025, 13:14:17) [GCC 11.2.0]\nCUDA available: True\nGPU 0: NVIDIA RTX 2000 Ada Generation\nCUDA_HOME: /usr/local/cuda\nNVCC: Cuda compilation tools, release 11.6, V11.6.124\nGCC: gcc (Ubuntu 9.4.0-1ubuntu1~20.04.2) 9.4.0\nPyTorch: 1.13.0+cu116\nPyTorch compiling details: PyTorch built with:\n  - GCC 9.3\n  - C++ Version: 201402\n  - Intel(R) Math Kernel Library Version 2020.0.0 Product Build 20191122 for Intel(R) 64 architecture applications\n  - Intel(R) MKL-DNN v2.6.0 (Git Hash 52b5f107dd9cf10910aaa19cb47f3abf9b349815)\n  - OpenMP 201511 (a.k.a. OpenMP 4.5)\n  - LAPACK is enabled (usually provided by MKL)\n  - NNPACK is enabled\n  - CPU capability usage: AVX2\n  - CUDA Runtime 11.6\n  - NVCC architecture flags: -gencode;arch=compute_37,code=sm_37;-gencode;arch=compute_50,code=sm_50;-gencode;arch=compute_60,code=sm_60;-gencode;arch=compute_70,code=sm_70;-gencode;arch=compute_75,code=sm_75;-gencode;arch=compute_80,code=sm_80;-gencode;arch=compute_86,code=sm_86\n  - CuDNN 8.3.2  (built against CUDA 11.5)\n  - Magma 2.6.1\n  - Build settings: BLAS_INFO=mkl, BUILD_TYPE=Release, CUDA_VERSION=11.6, CUDNN_VERSION=8.3.2, CXX_COMPILER=/opt/rh/devtoolset-9/root/usr/bin/c++, CXX_FLAGS= -fabi-version=11 -Wno-deprecated -fvisibility-inlines-hidden -DUSE_PTHREADPOOL -fopenmp -DNDEBUG -DUSE_KINETO -DUSE_FBGEMM -DUSE_QNNPACK -DUSE_PYTORCH_QNNPACK -DUSE_XNNPACK -DSYMBOLICATE_MOBILE_DEBUG_HANDLE -DEDGE_PROFILER_USE_KINETO -O2 -fPIC -Wno-narrowing -Wall -Wextra -Werror=return-type -Werror=non-virtual-dtor -Wno-missing-field-initializers -Wno-type-limits -Wno-array-bounds -Wno-unknown-pragmas -Wunused-local-typedefs -Wno-unused-parameter -Wno-unused-function -Wno-unused-result -Wno-strict-overflow -Wno-strict-aliasing -Wno-error=deprecated-declarations -Wno-stringop-overflow -Wno-psabi -Wno-error=pedantic -Wno-error=redundant-decls -Wno-error=old-style-cast -fdiagnostics-color=always -faligned-new -Wno-unused-but-set-variable -Wno-maybe-uninitialized -fno-math-errno -fno-trapping-math -Werror=format -Werror=cast-function-type -Wno-stringop-overflow, LAPACK_INFO=mkl, PERF_WITH_AVX=1, PERF_WITH_AVX2=1, PERF_WITH_AVX512=1, TORCH_VERSION=1.13.0, USE_CUDA=ON, USE_CUDNN=ON, USE_EXCEPTION_PTR=1, USE_GFLAGS=OFF, USE_GLOG=OFF, USE_MKL=ON, USE_MKLDNN=ON, USE_MPI=OFF, USE_NCCL=ON, USE_NNPACK=ON, USE_OPENMP=ON, USE_ROCM=OFF, \n\nTorchVision: 0.14.0+cu116\nOpenCV: 4.11.0\nMMCV: 1.7.1\nMMCV Compiler: GCC 9.4\nMMCV CUDA Compiler: 11.6\nMMDetection: 2.28.2+unknown", "config": "plugin = True\nplugin_dir = 'projects/mmdet3d_plugin/'\ndist_params = dict(backend='nccl')\nlog_level = 'INFO'\nwork_dir = './work_dirs/sparse4dv3_temporal_r50_1x8_bs6_256x704_local'\ntotal_batch_size = 1\nnum_gpus = 1\nbatch_size = 1\nnum_iters_per_epoch = 28130\nnum_epochs = 100\ncheckpoint_epoch_interval = 20\ncheckpoint_config = dict(interval=562600)\nlog_config = dict(\n    interval=1,\n    hooks=[\n        dict(type='TextLoggerHook', by_epoch=False),\n        dict(type='TensorboardLoggerHook')\n    ])\nload_from = None\nresume_from = None\nworkflow = [('train', 1)]\nfp16 = dict(loss_scale=32.0)\ninput_shape = (704, 256)\ntracking_test = True\ntracking_threshold = 0.2\nclass_names = [\n    'car', 'truck', 'construction_vehicle', 'bus', 'trailer', 'barrier',\n    'motorcycle', 'bicycle', 'pedestrian', 'traffic_cone'\n]\nnum_classes = 10\nembed_dims = 256\nnum_groups = 8\nnum_decoder = 6\nnum_single_frame_decoder = 1\nuse_deformable_func = True\nstrides = [4, 8, 16, 32]\nnum_levels = 4\nnum_depth_layers = 3\ndrop_out = 0.1\ntemporal = True\ndecouple_attn = True\nwith_quality_estimation = True\nmodel = dict(\n    type='Sparse4D',\n    use_grid_mask=True,\n    use_deformable_func=True,\n    img_backbone=dict(\n        type='ResNet',\n        depth=50,\n        num_stages=4,\n        frozen_stages=-1,\n        norm_eval=False,\n        style='pytorch',\n        with_cp=True,\n        out_indices=(0, 1, 2, 3),\n        norm_cfg=dict(type='BN', requires_grad=True),\n        pretrained='ckpt/resnet50-19c8e357.pth'),\n    img_neck=dict(\n        type='FPN',\n        num_outs=4,\n        start_level=0,\n        out_channels=256,\n        add_extra_convs='on_output',\n        relu_before_extra_convs=True,\n        in_channels=[256, 512, 1024, 2048]),\n    depth_branch=dict(\n        type='DenseDepthNet',\n        embed_dims=256,\n        num_depth_layers=3,\n        loss_weight=0.2),\n    head=dict(\n        type='Sparse4DHead',\n        cls_threshold_to_reg=0.05,\n        decouple_attn=True,\n        instance_bank=dict(\n            type='InstanceBank',\n            num_anchor=900,\n            embed_dims=256,\n            anchor='nuscenes_kmeans900.npy',\n            anchor_handler=dict(type='SparseBox3DKeyPointsGenerator'),\n            num_temp_instances=600,\n            confidence_decay=0.6,\n            feat_grad=False),\n        anchor_encoder=dict(\n            type='SparseBox3DEncoder',\n            vel_dims=3,\n            embed_dims=[128, 32, 32, 64],\n            mode='cat',\n            output_fc=False,\n            in_loops=1,\n            out_loops=4),\n        num_single_frame_decoder=1,\n        operation_order=[\n            'deformable', 'ffn', 'norm', 'refine', 'temp_gnn', 'gnn', 'norm',\n            'deformable', 'ffn', 'norm', 'refine', 'temp_gnn', 'gnn', 'norm',\n            'deformable', 'ffn', 'norm', 'refine', 'temp_gnn', 'gnn', 'norm',\n            'deformable', 'ffn', 'norm', 'refine', 'temp_gnn', 'gnn', 'norm',\n            'deformable', 'ffn', 'norm', 'refine', 'temp_gnn', 'gnn', 'norm',\n            'deformable', 'ffn', 'norm', 'refine'\n        ],\n        temp_graph_model=dict(\n            type='MultiheadAttention',\n            embed_dims=512,\n            num_heads=8,\n            batch_first=True,\n            dropout=0.1),\n        graph_model=dict(\n            type='MultiheadAttention',\n            embed_dims=512,\n            num_heads=8,\n            batch_first=True,\n            dropout=0.1),\n        norm_layer=dict(type='LN', normalized_shape=256),\n        ffn=dict(\n            type='AsymmetricFFN',\n            in_channels=512,\n            pre_norm=dict(type='LN'),\n            embed_dims=256,\n            feedforward_channels=1024,\n            num_fcs=2,\n            ffn_drop=0.1,\n            act_cfg=dict(type='ReLU', inplace=True)),\n        deformable_model=dict(\n            type='DeformableFeatureAggregation',\n            embed_dims=256,\n            num_groups=8,\n            num_levels=4,\n            num_cams=6,\n            attn_drop=0.15,\n            use_deformable_func=True,\n            use_camera_embed=True,\n            residual_mode='cat',\n            kps_generator=dict(\n                type='SparseBox3DKeyPointsGenerator',\n                num_learnable_pts=6,\n                fix_scale=[[0, 0, 0], [0.45, 0, 0], [-0.45, 0, 0],\n                           [0, 0.45, 0], [0, -0.45, 0], [0, 0, 0.45],\n                           [0, 0, -0.45]])),\n        refine_layer=dict(\n            type='SparseBox3DRefinementModule',\n            embed_dims=256,\n            num_cls=10,\n            refine_yaw=True,\n            with_quality_estimation=True),\n        sampler=dict(\n            type='SparseBox3DTarget',\n            num_dn_groups=5,\n            num_temp_dn_groups=3,\n            dn_noise_scale=[2.0, 2.0, 2.0, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5],\n            max_dn_gt=32,\n            add_neg_dn=True,\n            cls_weight=2.0,\n            box_weight=0.25,\n            reg_weights=[2.0, 2.0, 2.0, 0.5, 0.5, 0.5, 0.0, 0.0, 0.0, 0.0],\n            cls_wise_reg_weights=dict(\n                {9: [2.0, 2.0, 2.0, 1.0, 1.0, 1.0, 0.0, 0.0, 1.0, 1.0]})),\n        loss_cls=dict(\n            type='FocalLoss',\n            use_sigmoid=True,\n            gamma=2.0,\n            alpha=0.25,\n            loss_weight=2.0),\n        loss_reg=dict(\n            type='SparseBox3DLoss',\n            loss_box=dict(type='L1Loss', loss_weight=0.25),\n            loss_centerness=dict(type='CrossEntropyLoss', use_sigmoid=True),\n            loss_yawness=dict(type='GaussianFocalLoss'),\n            cls_allow_reverse=[5]),\n        decoder=dict(type='SparseBox3DDecoder'),\n        reg_weights=[2.0, 2.0, 2.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0]))\ndataset_type = 'NuScenes3DDetTrackDataset'\ndata_root = 'data/nuscenes/'\nanno_root = 'data/nuscenes_anno_pkls/'\nfile_client_args = dict(backend='disk')\nimg_norm_cfg = dict(\n    mean=[123.675, 116.28, 103.53], std=[58.395, 57.12, 57.375], to_rgb=True)\ntrain_pipeline = [\n    dict(type='LoadMultiViewImageFromFiles', to_float32=True),\n    dict(\n        type='LoadPointsFromFile',\n        coord_type='LIDAR',\n        load_dim=5,\n        use_dim=5,\n        file_client_args=dict(backend='disk')),\n    dict(type='ResizeCropFlipImage'),\n    dict(type='MultiScaleDepthMapGenerator', downsample=[4, 8, 16]),\n    dict(type='BBoxRotation'),\n    dict(type='PhotoMetricDistortionMultiViewImage'),\n    dict(\n        type='NormalizeMultiviewImage',\n        mean=[123.675, 116.28, 103.53],\n        std=[58.395, 57.12, 57.375],\n        to_rgb=True),\n    dict(\n        type='CircleObjectRangeFilter',\n        class_dist_thred=[55, 55, 55, 55, 55, 55, 55, 55, 55, 55]),\n    dict(\n        type='InstanceNameFilter',\n        classes=[\n            'car', 'truck', 'construction_vehicle', 'bus', 'trailer',\n            'barrier', 'motorcycle', 'bicycle', 'pedestrian', 'traffic_cone'\n        ]),\n    dict(type='NuScenesSparse4DAdaptor'),\n    dict(\n        type='Collect',\n        keys=[\n            'img', 'timestamp', 'projection_mat', 'image_wh', 'gt_depth',\n            'focal', 'gt_bboxes_3d', 'gt_labels_3d'\n        ],\n        meta_keys=['T_global', 'T_global_inv', 'timestamp', 'instance_id'])\n]\ntest_pipeline = [\n    dict(type='LoadMultiViewImageFromFiles', to_float32=True),\n    dict(type='ResizeCropFlipImage'),\n    dict(\n        type='NormalizeMultiviewImage',\n        mean=[123.675, 116.28, 103.53],\n        std=[58.395, 57.12, 57.375],\n        to_rgb=True),\n    dict(type='NuScenesSparse4DAdaptor'),\n    dict(\n        type='Collect',\n        keys=['img', 'timestamp', 'projection_mat', 'image_wh'],\n        meta_keys=['T_global', 'T_global_inv', 'timestamp'])\n]\ninput_modality = dict(\n    use_lidar=False,\n    use_camera=True,\n    use_radar=False,\n    use_map=False,\n    use_external=False)\ndata_basic_config = dict(\n    type='NuScenes3DDetTrackDataset',\n    data_root='data/nuscenes/',\n    classes=[\n        'car', 'truck', 'construction_vehicle', 'bus', 'trailer', 'barrier',\n        'motorcycle', 'bicycle', 'pedestrian', 'traffic_cone'\n    ],\n    modality=dict(\n        use_lidar=False,\n        use_camera=True,\n        use_radar=False,\n        use_map=False,\n        use_external=False),\n    version='v1.0-mini')\ndata_aug_conf = dict(\n    resize_lim=(0.4, 0.47),\n    final_dim=(256, 704),\n    bot_pct_lim=(0.0, 0.0),\n    rot_lim=(-5.4, 5.4),\n    H=900,\n    W=1600,\n    rand_flip=True,\n    rot3d_range=[-0.3925, 0.3925])\ndata = dict(\n    samples_per_gpu=1,\n    workers_per_gpu=1,\n    train=dict(\n        type='NuScenes3DDetTrackDataset',\n        data_root='data/nuscenes/',\n        classes=[\n            'car', 'truck', 'construction_vehicle', 'bus', 'trailer',\n            'barrier', 'motorcycle', 'bicycle', 'pedestrian', 'traffic_cone'\n        ],\n        modality=dict(\n            use_lidar=False,\n            use_camera=True,\n            use_radar=False,\n            use_map=False,\n            use_external=False),\n        version='v1.0-mini',\n        ann_file='data/nuscenes_anno_pkls/nuscenes-mini_infos_train.pkl',\n        pipeline=[\n            dict(type='LoadMultiViewImageFromFiles', to_float32=True),\n            dict(\n                type='LoadPointsFromFile',\n                coord_type='LIDAR',\n                load_dim=5,\n                use_dim=5,\n                file_client_args=dict(backend='disk')),\n            dict(type='ResizeCropFlipImage'),\n            dict(type='MultiScaleDepthMapGenerator', downsample=[4, 8, 16]),\n            dict(type='BBoxRotation'),\n            dict(type='PhotoMetricDistortionMultiViewImage'),\n            dict(\n                type='NormalizeMultiviewImage',\n                mean=[123.675, 116.28, 103.53],\n                std=[58.395, 57.12, 57.375],\n                to_rgb=True),\n            dict(\n                type='CircleObjectRangeFilter',\n                class_dist_thred=[55, 55, 55, 55, 55, 55, 55, 55, 55, 55]),\n            dict(\n                type='InstanceNameFilter',\n                classes=[\n                    'car', 'truck', 'construction_vehicle', 'bus', 'trailer',\n                    'barrier', 'motorcycle', 'bicycle', 'pedestrian',\n                    'traffic_cone'\n                ]),\n            dict(type='NuScenesSparse4DAdaptor'),\n            dict(\n                type='Collect',\n                keys=[\n                    'img', 'timestamp', 'projection_mat', 'image_wh',\n                    'gt_depth', 'focal', 'gt_bboxes_3d', 'gt_labels_3d'\n                ],\n                meta_keys=[\n                    'T_global', 'T_global_inv', 'timestamp', 'instance_id'\n                ])\n        ],\n        test_mode=False,\n        data_aug_conf=dict(\n            resize_lim=(0.4, 0.47),\n            final_dim=(256, 704),\n            bot_pct_lim=(0.0, 0.0),\n            rot_lim=(-5.4, 5.4),\n            H=900,\n            W=1600,\n            rand_flip=True,\n            rot3d_range=[-0.3925, 0.3925]),\n        with_seq_flag=True,\n        sequences_split_num=2,\n        keep_consistent_seq_aug=True),\n    val=dict(\n        type='NuScenes3DDetTrackDataset',\n        data_root='data/nuscenes/',\n        classes=[\n            'car', 'truck', 'construction_vehicle', 'bus', 'trailer',\n            'barrier', 'motorcycle', 'bicycle', 'pedestrian', 'traffic_cone'\n        ],\n        modality=dict(\n            use_lidar=False,\n            use_camera=True,\n            use_radar=False,\n            use_map=False,\n            use_external=False),\n        version='v1.0-mini',\n        ann_file='data/nuscenes_anno_pkls/nuscenes-mini_infos_val.pkl',\n        pipeline=[\n            dict(type='LoadMultiViewImageFromFiles', to_float32=True),\n            dict(type='ResizeCropFlipImage'),\n            dict(\n                type='NormalizeMultiviewImage',\n                mean=[123.675, 116.28, 103.53],\n                std=[58.395, 57.12, 57.375],\n                to_rgb=True),\n            dict(type='NuScenesSparse4DAdaptor'),\n            dict(\n                type='Collect',\n                keys=['img', 'timestamp', 'projection_mat', 'image_wh'],\n                meta_keys=['T_global', 'T_global_inv', 'timestamp'])\n        ],\n        data_aug_conf=dict(\n            resize_lim=(0.4, 0.47),\n            final_dim=(256, 704),\n            bot_pct_lim=(0.0, 0.0),\n            rot_lim=(-5.4, 5.4),\n            H=900,\n            W=1600,\n            rand_flip=True,\n            rot3d_range=[-0.3925, 0.3925]),\n        test_mode=True,\n        tracking=True,\n        tracking_threshold=0.2),\n    test=dict(\n        type='NuScenes3DDetTrackDataset',\n        data_root='data/nuscenes/',\n        classes=[\n            'car', 'truck', 'construction_vehicle', 'bus', 'trailer',\n            'barrier', 'motorcycle', 'bicycle', 'pedestrian', 'traffic_cone'\n        ],\n        modality=dict(\n            use_lidar=False,\n            use_camera=True,\n            use_radar=False,\n            use_map=False,\n            use_external=False),\n        version='v1.0-mini',\n        ann_file='data/nuscenes_anno_pkls/nuscenes-mini_infos_val.pkl',\n        pipeline=[\n            dict(type='LoadMultiViewImageFromFiles', to_float32=True),\n            dict(type='ResizeCropFlipImage'),\n            dict(\n                type='NormalizeMultiviewImage',\n                mean=[123.675, 116.28, 103.53],\n                std=[58.395, 57.12, 57.375],\n                to_rgb=True),\n            dict(type='NuScenesSparse4DAdaptor'),\n            dict(\n                type='Collect',\n                keys=['img', 'timestamp', 'projection_mat', 'image_wh'],\n                meta_keys=['T_global', 'T_global_inv', 'timestamp'])\n        ],\n        data_aug_conf=dict(\n            resize_lim=(0.4, 0.47),\n            final_dim=(256, 704),\n            bot_pct_lim=(0.0, 0.0),\n            rot_lim=(-5.4, 5.4),\n            H=900,\n            W=1600,\n            rand_flip=True,\n            rot3d_range=[-0.3925, 0.3925]),\n        test_mode=True,\n        tracking=True,\n        tracking_threshold=0.2))\noptimizer = dict(\n    type='AdamW',\n    lr=0.0006,\n    weight_decay=0.001,\n    paramwise_cfg=dict(custom_keys=dict(img_backbone=dict(lr_mult=0.5))))\noptimizer_config = dict(grad_clip=dict(max_norm=25, norm_type=2))\nlr_config = dict(\n    policy='CosineAnnealing',\n    warmup='linear',\n    warmup_iters=500,\n    warmup_ratio=0.3333333333333333,\n    min_lr_ratio=0.001)\nrunner = dict(type='IterBasedRunner', max_iters=2813000)\nvis_pipeline = [\n    dict(type='LoadMultiViewImageFromFiles', to_float32=True),\n    dict(type='Collect', keys=['img'], meta_keys=['timestamp', 'lidar2img'])\n]\nevaluation = dict(\n    interval=562600,\n    pipeline=[\n        dict(type='LoadMultiViewImageFromFiles', to_float32=True),\n        dict(\n            type='Collect', keys=['img'], meta_keys=['timestamp', 'lidar2img'])\n    ])\ngpu_ids = range(0, 1)\n", "seed": 0, "exp_name": "sparse4dv3_temporal_r50_1x8_bs6_256x704_local.py"}
{"mode": "train", "epoch": 1, "iter": 1, "lr": 0.0001, "memory": 2558, "data_time": 0.25478, "loss_cls_0": 2.41307, "loss_box_0": 0.05608, "loss_cns_0": 0.01418, "loss_yns_0": 0.00999, "loss_cls_1": 2.25198, "loss_box_1": 0.0, "loss_cns_1": 0.0, "loss_yns_1": 0.0, "loss_cls_2": 2.80785, "loss_box_2": 0.0, "loss_cns_2": 0.0, "loss_yns_2": 0.0, "loss_cls_3": 2.80916, "loss_box_3": 0.0, "loss_cns_3": 0.0, "loss_yns_3": 0.0, "loss_cls_4": 1.79493, "loss_box_4": 0.57156, "loss_cns_4": 0.0604, "loss_yns_4": 0.02789, "loss_cls_5": 2.09616, "loss_box_5": 0.0, "loss_cns_5": 0.0, "loss_yns_5": 0.0, "loss_cls_dn_0": 1.18384, "loss_box_dn_0": 1.49398, "loss_cls_dn_1": 1.10162, "loss_box_dn_1": 1.78668, "loss_cls_dn_2": 1.41841, "loss_box_dn_2": 2.10171, "loss_cls_dn_3": 1.20829, "loss_box_dn_3": 2.29093, "loss_cls_dn_4": 0.94449, "loss_box_dn_4": 2.38403, "loss_cls_dn_5": 1.11921, "loss_box_dn_5": 2.51601, "loss_dense_depth": 2.13702, "loss": 36.59945, "grad_norm": 537.68536, "time": 2.12576}
{"mode": "train", "epoch": 1, "iter": 2, "lr": 0.0001, "memory": 2845, "data_time": 0.00526, "loss_cls_0": 1.96951, "loss_box_0": 0.0, "loss_cns_0": 0.0, "loss_yns_0": 0.0, "loss_cls_1": 1.93054, "loss_box_1": 0.27476, "loss_cns_1": 0.05708, "loss_yns_1": 0.00682, "loss_cls_2": 2.22933, "loss_box_2": 0.0, "loss_cns_2": 0.0, "loss_yns_2": 0.0, "loss_cls_3": 1.91965, "loss_box_3": 0.38996, "loss_cns_3": 0.04863, "loss_yns_3": 0.00673, "loss_cls_4": 1.44906, "loss_box_4": 1.87592, "loss_cns_4": 0.21735, "loss_yns_4": 0.0647, "loss_cls_5": 1.54114, "loss_box_5": 2.73092, "loss_cns_5": 0.376, "loss_yns_5": 0.22566, "loss_cls_dn_0": 0.98769, "loss_box_dn_0": 1.33402, "loss_cls_dn_1": 0.84451, "loss_box_dn_1": 2.41412, "loss_cls_dn_2": 0.97372, "loss_box_dn_2": 2.43487, "loss_cls_dn_3": 0.82263, "loss_box_dn_3": 2.43092, "loss_cls_dn_4": 0.69609, "loss_box_dn_4": 2.55436, "loss_cls_dn_5": 0.70513, "loss_box_dn_5": 2.69186, "loss_dense_depth": 1.94554, "loss": 38.14922, "grad_norm": 100.14013, "time": 0.55683}
{"mode": "train", "epoch": 1, "iter": 3, "lr": 0.0001, "memory": 2847, "data_time": 0.00519, "loss_cls_0": 1.2713, "loss_box_0": 2.96502, "loss_cns_0": 0.75981, "loss_yns_0": 0.18622, "loss_cls_1": 1.47921, "loss_box_1": 1.96787, "loss_cns_1": 0.31048, "loss_yns_1": 0.15942, "loss_cls_2": 1.69087, "loss_box_2": 0.45184, "loss_cns_2": 0.0428, "loss_yns_2": 0.01507, "loss_cls_3": 1.28448, "loss_box_3": 3.67295, "loss_cns_3": 0.42523, "loss_yns_3": 0.17742, "loss_cls_4": 1.0107, "loss_box_4": 4.70992, "loss_cns_4": 0.46436, "loss_yns_4": 0.18671, "loss_cls_5": 1.09215, "loss_box_5": 4.86696, "loss_cns_5": 0.43301, "loss_yns_5": 0.19113, "loss_cls_dn_0": 0.6511, "loss_box_dn_0": 1.26468, "loss_cls_dn_1": 0.67174, "loss_box_dn_1": 2.40205, "loss_cls_dn_2": 0.71922, "loss_box_dn_2": 2.39859, "loss_cls_dn_3": 0.57884, "loss_box_dn_3": 2.36335, "loss_cls_dn_4": 0.43077, "loss_box_dn_4": 2.50644, "loss_cls_dn_5": 0.48405, "loss_box_dn_5": 2.67757, "loss_dense_depth": 1.91414, "loss": 48.87746, "grad_norm": 135.29301, "time": 0.55792}
{"mode": "train", "epoch": 1, "iter": 4, "lr": 0.0001, "memory": 2847, "data_time": 0.0055, "loss_cls_0": 1.12365, "loss_box_0": 2.59156, "loss_cns_0": 0.63134, "loss_yns_0": 0.15523, "loss_cls_1": 1.14297, "loss_box_1": 2.4047, "loss_cns_1": 0.54165, "loss_yns_1": 0.2478, "loss_cls_2": 1.13756, "loss_box_2": 2.50048, "loss_cns_2": 0.49167, "loss_yns_2": 0.2109, "loss_cls_3": 0.94979, "loss_box_3": 3.80842, "loss_cns_3": 0.41634, "loss_yns_3": 0.23591, "loss_cls_4": 1.03148, "loss_box_4": 4.76126, "loss_cns_4": 0.36448, "loss_yns_4": 0.19504, "loss_cls_5": 1.2325, "loss_box_5": 4.87256, "loss_cns_5": 0.43897, "loss_yns_5": 0.18538, "loss_cls_dn_0": 0.5405, "loss_box_dn_0": 1.25261, "loss_cls_dn_1": 0.6276, "loss_box_dn_1": 2.15632, "loss_cls_dn_2": 0.6151, "loss_box_dn_2": 2.32303, "loss_cls_dn_3": 0.46037, "loss_box_dn_3": 2.60176, "loss_cls_dn_4": 0.38914, "loss_box_dn_4": 2.92564, "loss_cls_dn_5": 0.4138, "loss_box_dn_5": 3.16995, "loss_dense_depth": 1.86543, "loss": 51.0129, "grad_norm": 150.0719, "time": 0.55645}
{"mode": "train", "epoch": 1, "iter": 5, "lr": 0.0001, "memory": 2847, "data_time": 0.0055, "loss_cls_0": 1.22113, "loss_box_0": 2.59918, "loss_cns_0": 0.51323, "loss_yns_0": 0.16581, "loss_cls_1": 1.03584, "loss_box_1": 3.0105, "loss_cns_1": 0.4818, "loss_yns_1": 0.21782, "loss_cls_2": 0.95879, "loss_box_2": 3.03963, "loss_cns_2": 0.47546, "loss_yns_2": 0.21561, "loss_cls_3": 0.9214, "loss_box_3": 3.07111, "loss_cns_3": 0.51848, "loss_yns_3": 0.16708, "loss_cls_4": 1.03254, "loss_box_4": 3.39987, "loss_cns_4": 0.55146, "loss_yns_4": 0.20707, "loss_cls_5": 0.95261, "loss_box_5": 3.83132, "loss_cns_5": 0.46505, "loss_yns_5": 0.21905, "loss_cls_dn_0": 0.51684, "loss_box_dn_0": 1.2782, "loss_cls_dn_1": 0.53171, "loss_box_dn_1": 2.65414, "loss_cls_dn_2": 0.51903, "loss_box_dn_2": 2.74749, "loss_cls_dn_3": 0.38401, "loss_box_dn_3": 2.90197, "loss_cls_dn_4": 0.35591, "loss_box_dn_4": 3.06104, "loss_cls_dn_5": 0.41656, "loss_box_dn_5": 3.19113, "loss_dense_depth": 1.72711, "loss": 49.55699, "grad_norm": 162.64258, "time": 0.55504}
