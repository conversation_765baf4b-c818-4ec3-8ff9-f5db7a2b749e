{"env_info": "sys.platform: linux\nPython: 3.10.18 (main, Jun  5 2025, 13:14:17) [GCC 11.2.0]\nCUDA available: True\nGPU 0: NVIDIA RTX 2000 Ada Generation\nCUDA_HOME: /usr/local/cuda\nNVCC: Cuda compilation tools, release 11.6, V11.6.124\nGCC: gcc (Ubuntu 9.4.0-1ubuntu1~20.04.2) 9.4.0\nPyTorch: 1.13.0+cu116\nPyTorch compiling details: PyTorch built with:\n  - GCC 9.3\n  - C++ Version: 201402\n  - Intel(R) Math Kernel Library Version 2020.0.0 Product Build 20191122 for Intel(R) 64 architecture applications\n  - Intel(R) MKL-DNN v2.6.0 (Git Hash 52b5f107dd9cf10910aaa19cb47f3abf9b349815)\n  - OpenMP 201511 (a.k.a. OpenMP 4.5)\n  - LAPACK is enabled (usually provided by MKL)\n  - NNPACK is enabled\n  - CPU capability usage: AVX2\n  - CUDA Runtime 11.6\n  - NVCC architecture flags: -gencode;arch=compute_37,code=sm_37;-gencode;arch=compute_50,code=sm_50;-gencode;arch=compute_60,code=sm_60;-gencode;arch=compute_70,code=sm_70;-gencode;arch=compute_75,code=sm_75;-gencode;arch=compute_80,code=sm_80;-gencode;arch=compute_86,code=sm_86\n  - CuDNN 8.3.2  (built against CUDA 11.5)\n  - Magma 2.6.1\n  - Build settings: BLAS_INFO=mkl, BUILD_TYPE=Release, CUDA_VERSION=11.6, CUDNN_VERSION=8.3.2, CXX_COMPILER=/opt/rh/devtoolset-9/root/usr/bin/c++, CXX_FLAGS= -fabi-version=11 -Wno-deprecated -fvisibility-inlines-hidden -DUSE_PTHREADPOOL -fopenmp -DNDEBUG -DUSE_KINETO -DUSE_FBGEMM -DUSE_QNNPACK -DUSE_PYTORCH_QNNPACK -DUSE_XNNPACK -DSYMBOLICATE_MOBILE_DEBUG_HANDLE -DEDGE_PROFILER_USE_KINETO -O2 -fPIC -Wno-narrowing -Wall -Wextra -Werror=return-type -Werror=non-virtual-dtor -Wno-missing-field-initializers -Wno-type-limits -Wno-array-bounds -Wno-unknown-pragmas -Wunused-local-typedefs -Wno-unused-parameter -Wno-unused-function -Wno-unused-result -Wno-strict-overflow -Wno-strict-aliasing -Wno-error=deprecated-declarations -Wno-stringop-overflow -Wno-psabi -Wno-error=pedantic -Wno-error=redundant-decls -Wno-error=old-style-cast -fdiagnostics-color=always -faligned-new -Wno-unused-but-set-variable -Wno-maybe-uninitialized -fno-math-errno -fno-trapping-math -Werror=format -Werror=cast-function-type -Wno-stringop-overflow, LAPACK_INFO=mkl, PERF_WITH_AVX=1, PERF_WITH_AVX2=1, PERF_WITH_AVX512=1, TORCH_VERSION=1.13.0, USE_CUDA=ON, USE_CUDNN=ON, USE_EXCEPTION_PTR=1, USE_GFLAGS=OFF, USE_GLOG=OFF, USE_MKL=ON, USE_MKLDNN=ON, USE_MPI=OFF, USE_NCCL=ON, USE_NNPACK=ON, USE_OPENMP=ON, USE_ROCM=OFF, \n\nTorchVision: 0.14.0+cu116\nOpenCV: 4.11.0\nMMCV: 1.7.1\nMMCV Compiler: GCC 9.4\nMMCV CUDA Compiler: 11.6\nMMDetection: 2.28.2+unknown", "config": "plugin = True\nplugin_dir = 'projects/mmdet3d_plugin/'\ndist_params = dict(backend='nccl')\nlog_level = 'INFO'\nwork_dir = './work_dirs/sparse4dv3_temporal_r50_1x8_bs6_256x704_local'\ntotal_batch_size = 1\nnum_gpus = 1\nbatch_size = 1\nnum_iters_per_epoch = 28130\nnum_epochs = 100\ncheckpoint_epoch_interval = 20\ncheckpoint_config = dict(interval=562600)\nlog_config = dict(\n    interval=1,\n    hooks=[\n        dict(type='TextLoggerHook', by_epoch=False),\n        dict(type='TensorboardLoggerHook')\n    ])\nload_from = None\nresume_from = None\nworkflow = [('train', 1)]\nfp16 = dict(loss_scale=32.0)\ninput_shape = (704, 256)\ntracking_test = True\ntracking_threshold = 0.2\nclass_names = [\n    'car', 'truck', 'construction_vehicle', 'bus', 'trailer', 'barrier',\n    'motorcycle', 'bicycle', 'pedestrian', 'traffic_cone'\n]\nnum_classes = 10\nembed_dims = 256\nnum_groups = 8\nnum_decoder = 6\nnum_single_frame_decoder = 1\nuse_deformable_func = True\nstrides = [4, 8, 16, 32]\nnum_levels = 4\nnum_depth_layers = 3\ndrop_out = 0.1\ntemporal = True\ndecouple_attn = True\nwith_quality_estimation = True\nmodel = dict(\n    type='Sparse4D',\n    use_grid_mask=True,\n    use_deformable_func=True,\n    img_backbone=dict(\n        type='ResNet',\n        depth=50,\n        num_stages=4,\n        frozen_stages=-1,\n        norm_eval=False,\n        style='pytorch',\n        with_cp=True,\n        out_indices=(0, 1, 2, 3),\n        norm_cfg=dict(type='BN', requires_grad=True),\n        pretrained='ckpt/resnet50-19c8e357.pth'),\n    img_neck=dict(\n        type='FPN',\n        num_outs=4,\n        start_level=0,\n        out_channels=256,\n        add_extra_convs='on_output',\n        relu_before_extra_convs=True,\n        in_channels=[256, 512, 1024, 2048]),\n    depth_branch=dict(\n        type='DenseDepthNet',\n        embed_dims=256,\n        num_depth_layers=3,\n        loss_weight=0.2),\n    head=dict(\n        type='Sparse4DHead',\n        cls_threshold_to_reg=0.05,\n        decouple_attn=True,\n        instance_bank=dict(\n            type='InstanceBank',\n            num_anchor=900,\n            embed_dims=256,\n            anchor='nuscenes_kmeans900.npy',\n            anchor_handler=dict(type='SparseBox3DKeyPointsGenerator'),\n            num_temp_instances=600,\n            confidence_decay=0.6,\n            feat_grad=False),\n        anchor_encoder=dict(\n            type='SparseBox3DEncoder',\n            vel_dims=3,\n            embed_dims=[128, 32, 32, 64],\n            mode='cat',\n            output_fc=False,\n            in_loops=1,\n            out_loops=4),\n        num_single_frame_decoder=1,\n        operation_order=[\n            'deformable', 'ffn', 'norm', 'refine', 'temp_gnn', 'gnn', 'norm',\n            'deformable', 'ffn', 'norm', 'refine', 'temp_gnn', 'gnn', 'norm',\n            'deformable', 'ffn', 'norm', 'refine', 'temp_gnn', 'gnn', 'norm',\n            'deformable', 'ffn', 'norm', 'refine', 'temp_gnn', 'gnn', 'norm',\n            'deformable', 'ffn', 'norm', 'refine', 'temp_gnn', 'gnn', 'norm',\n            'deformable', 'ffn', 'norm', 'refine'\n        ],\n        temp_graph_model=dict(\n            type='MultiheadAttention',\n            embed_dims=512,\n            num_heads=8,\n            batch_first=True,\n            dropout=0.1),\n        graph_model=dict(\n            type='MultiheadAttention',\n            embed_dims=512,\n            num_heads=8,\n            batch_first=True,\n            dropout=0.1),\n        norm_layer=dict(type='LN', normalized_shape=256),\n        ffn=dict(\n            type='AsymmetricFFN',\n            in_channels=512,\n            pre_norm=dict(type='LN'),\n            embed_dims=256,\n            feedforward_channels=1024,\n            num_fcs=2,\n            ffn_drop=0.1,\n            act_cfg=dict(type='ReLU', inplace=True)),\n        deformable_model=dict(\n            type='DeformableFeatureAggregation',\n            embed_dims=256,\n            num_groups=8,\n            num_levels=4,\n            num_cams=6,\n            attn_drop=0.15,\n            use_deformable_func=True,\n            use_camera_embed=True,\n            residual_mode='cat',\n            kps_generator=dict(\n                type='SparseBox3DKeyPointsGenerator',\n                num_learnable_pts=6,\n                fix_scale=[[0, 0, 0], [0.45, 0, 0], [-0.45, 0, 0],\n                           [0, 0.45, 0], [0, -0.45, 0], [0, 0, 0.45],\n                           [0, 0, -0.45]])),\n        refine_layer=dict(\n            type='SparseBox3DRefinementModule',\n            embed_dims=256,\n            num_cls=10,\n            refine_yaw=True,\n            with_quality_estimation=True),\n        sampler=dict(\n            type='SparseBox3DTarget',\n            num_dn_groups=5,\n            num_temp_dn_groups=3,\n            dn_noise_scale=[2.0, 2.0, 2.0, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5],\n            max_dn_gt=32,\n            add_neg_dn=True,\n            cls_weight=2.0,\n            box_weight=0.25,\n            reg_weights=[2.0, 2.0, 2.0, 0.5, 0.5, 0.5, 0.0, 0.0, 0.0, 0.0],\n            cls_wise_reg_weights=dict(\n                {9: [2.0, 2.0, 2.0, 1.0, 1.0, 1.0, 0.0, 0.0, 1.0, 1.0]})),\n        loss_cls=dict(\n            type='FocalLoss',\n            use_sigmoid=True,\n            gamma=2.0,\n            alpha=0.25,\n            loss_weight=2.0),\n        loss_reg=dict(\n            type='SparseBox3DLoss',\n            loss_box=dict(type='L1Loss', loss_weight=0.25),\n            loss_centerness=dict(type='CrossEntropyLoss', use_sigmoid=True),\n            loss_yawness=dict(type='GaussianFocalLoss'),\n            cls_allow_reverse=[5]),\n        decoder=dict(type='SparseBox3DDecoder'),\n        reg_weights=[2.0, 2.0, 2.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0]))\ndataset_type = 'NuScenes3DDetTrackDataset'\ndata_root = 'data/nuscenes/'\nanno_root = 'data/nuscenes_anno_pkls/'\nfile_client_args = dict(backend='disk')\nimg_norm_cfg = dict(\n    mean=[123.675, 116.28, 103.53], std=[58.395, 57.12, 57.375], to_rgb=True)\ntrain_pipeline = [\n    dict(type='LoadMultiViewImageFromFiles', to_float32=True),\n    dict(\n        type='LoadPointsFromFile',\n        coord_type='LIDAR',\n        load_dim=5,\n        use_dim=5,\n        file_client_args=dict(backend='disk')),\n    dict(type='ResizeCropFlipImage'),\n    dict(type='MultiScaleDepthMapGenerator', downsample=[4, 8, 16]),\n    dict(type='BBoxRotation'),\n    dict(type='PhotoMetricDistortionMultiViewImage'),\n    dict(\n        type='NormalizeMultiviewImage',\n        mean=[123.675, 116.28, 103.53],\n        std=[58.395, 57.12, 57.375],\n        to_rgb=True),\n    dict(\n        type='CircleObjectRangeFilter',\n        class_dist_thred=[55, 55, 55, 55, 55, 55, 55, 55, 55, 55]),\n    dict(\n        type='InstanceNameFilter',\n        classes=[\n            'car', 'truck', 'construction_vehicle', 'bus', 'trailer',\n            'barrier', 'motorcycle', 'bicycle', 'pedestrian', 'traffic_cone'\n        ]),\n    dict(type='NuScenesSparse4DAdaptor'),\n    dict(\n        type='Collect',\n        keys=[\n            'img', 'timestamp', 'projection_mat', 'image_wh', 'gt_depth',\n            'focal', 'gt_bboxes_3d', 'gt_labels_3d'\n        ],\n        meta_keys=['T_global', 'T_global_inv', 'timestamp', 'instance_id'])\n]\ntest_pipeline = [\n    dict(type='LoadMultiViewImageFromFiles', to_float32=True),\n    dict(type='ResizeCropFlipImage'),\n    dict(\n        type='NormalizeMultiviewImage',\n        mean=[123.675, 116.28, 103.53],\n        std=[58.395, 57.12, 57.375],\n        to_rgb=True),\n    dict(type='NuScenesSparse4DAdaptor'),\n    dict(\n        type='Collect',\n        keys=['img', 'timestamp', 'projection_mat', 'image_wh'],\n        meta_keys=['T_global', 'T_global_inv', 'timestamp'])\n]\ninput_modality = dict(\n    use_lidar=False,\n    use_camera=True,\n    use_radar=False,\n    use_map=False,\n    use_external=False)\ndata_basic_config = dict(\n    type='NuScenes3DDetTrackDataset',\n    data_root='data/nuscenes/',\n    classes=[\n        'car', 'truck', 'construction_vehicle', 'bus', 'trailer', 'barrier',\n        'motorcycle', 'bicycle', 'pedestrian', 'traffic_cone'\n    ],\n    modality=dict(\n        use_lidar=False,\n        use_camera=True,\n        use_radar=False,\n        use_map=False,\n        use_external=False),\n    version='v1.0-mini')\ndata_aug_conf = dict(\n    resize_lim=(0.4, 0.47),\n    final_dim=(256, 704),\n    bot_pct_lim=(0.0, 0.0),\n    rot_lim=(-5.4, 5.4),\n    H=900,\n    W=1600,\n    rand_flip=True,\n    rot3d_range=[-0.3925, 0.3925])\ndata = dict(\n    samples_per_gpu=1,\n    workers_per_gpu=1,\n    train=dict(\n        type='NuScenes3DDetTrackDataset',\n        data_root='data/nuscenes/',\n        classes=[\n            'car', 'truck', 'construction_vehicle', 'bus', 'trailer',\n            'barrier', 'motorcycle', 'bicycle', 'pedestrian', 'traffic_cone'\n        ],\n        modality=dict(\n            use_lidar=False,\n            use_camera=True,\n            use_radar=False,\n            use_map=False,\n            use_external=False),\n        version='v1.0-mini',\n        ann_file='data/nuscenes_anno_pkls/nuscenes-mini_infos_train.pkl',\n        pipeline=[\n            dict(type='LoadMultiViewImageFromFiles', to_float32=True),\n            dict(\n                type='LoadPointsFromFile',\n                coord_type='LIDAR',\n                load_dim=5,\n                use_dim=5,\n                file_client_args=dict(backend='disk')),\n            dict(type='ResizeCropFlipImage'),\n            dict(type='MultiScaleDepthMapGenerator', downsample=[4, 8, 16]),\n            dict(type='BBoxRotation'),\n            dict(type='PhotoMetricDistortionMultiViewImage'),\n            dict(\n                type='NormalizeMultiviewImage',\n                mean=[123.675, 116.28, 103.53],\n                std=[58.395, 57.12, 57.375],\n                to_rgb=True),\n            dict(\n                type='CircleObjectRangeFilter',\n                class_dist_thred=[55, 55, 55, 55, 55, 55, 55, 55, 55, 55]),\n            dict(\n                type='InstanceNameFilter',\n                classes=[\n                    'car', 'truck', 'construction_vehicle', 'bus', 'trailer',\n                    'barrier', 'motorcycle', 'bicycle', 'pedestrian',\n                    'traffic_cone'\n                ]),\n            dict(type='NuScenesSparse4DAdaptor'),\n            dict(\n                type='Collect',\n                keys=[\n                    'img', 'timestamp', 'projection_mat', 'image_wh',\n                    'gt_depth', 'focal', 'gt_bboxes_3d', 'gt_labels_3d'\n                ],\n                meta_keys=[\n                    'T_global', 'T_global_inv', 'timestamp', 'instance_id'\n                ])\n        ],\n        test_mode=False,\n        data_aug_conf=dict(\n            resize_lim=(0.4, 0.47),\n            final_dim=(256, 704),\n            bot_pct_lim=(0.0, 0.0),\n            rot_lim=(-5.4, 5.4),\n            H=900,\n            W=1600,\n            rand_flip=True,\n            rot3d_range=[-0.3925, 0.3925]),\n        with_seq_flag=True,\n        sequences_split_num=2,\n        keep_consistent_seq_aug=True),\n    val=dict(\n        type='NuScenes3DDetTrackDataset',\n        data_root='data/nuscenes/',\n        classes=[\n            'car', 'truck', 'construction_vehicle', 'bus', 'trailer',\n            'barrier', 'motorcycle', 'bicycle', 'pedestrian', 'traffic_cone'\n        ],\n        modality=dict(\n            use_lidar=False,\n            use_camera=True,\n            use_radar=False,\n            use_map=False,\n            use_external=False),\n        version='v1.0-mini',\n        ann_file='data/nuscenes_anno_pkls/nuscenes-mini_infos_val.pkl',\n        pipeline=[\n            dict(type='LoadMultiViewImageFromFiles', to_float32=True),\n            dict(type='ResizeCropFlipImage'),\n            dict(\n                type='NormalizeMultiviewImage',\n                mean=[123.675, 116.28, 103.53],\n                std=[58.395, 57.12, 57.375],\n                to_rgb=True),\n            dict(type='NuScenesSparse4DAdaptor'),\n            dict(\n                type='Collect',\n                keys=['img', 'timestamp', 'projection_mat', 'image_wh'],\n                meta_keys=['T_global', 'T_global_inv', 'timestamp'])\n        ],\n        data_aug_conf=dict(\n            resize_lim=(0.4, 0.47),\n            final_dim=(256, 704),\n            bot_pct_lim=(0.0, 0.0),\n            rot_lim=(-5.4, 5.4),\n            H=900,\n            W=1600,\n            rand_flip=True,\n            rot3d_range=[-0.3925, 0.3925]),\n        test_mode=True,\n        tracking=True,\n        tracking_threshold=0.2),\n    test=dict(\n        type='NuScenes3DDetTrackDataset',\n        data_root='data/nuscenes/',\n        classes=[\n            'car', 'truck', 'construction_vehicle', 'bus', 'trailer',\n            'barrier', 'motorcycle', 'bicycle', 'pedestrian', 'traffic_cone'\n        ],\n        modality=dict(\n            use_lidar=False,\n            use_camera=True,\n            use_radar=False,\n            use_map=False,\n            use_external=False),\n        version='v1.0-mini',\n        ann_file='data/nuscenes_anno_pkls/nuscenes-mini_infos_val.pkl',\n        pipeline=[\n            dict(type='LoadMultiViewImageFromFiles', to_float32=True),\n            dict(type='ResizeCropFlipImage'),\n            dict(\n                type='NormalizeMultiviewImage',\n                mean=[123.675, 116.28, 103.53],\n                std=[58.395, 57.12, 57.375],\n                to_rgb=True),\n            dict(type='NuScenesSparse4DAdaptor'),\n            dict(\n                type='Collect',\n                keys=['img', 'timestamp', 'projection_mat', 'image_wh'],\n                meta_keys=['T_global', 'T_global_inv', 'timestamp'])\n        ],\n        data_aug_conf=dict(\n            resize_lim=(0.4, 0.47),\n            final_dim=(256, 704),\n            bot_pct_lim=(0.0, 0.0),\n            rot_lim=(-5.4, 5.4),\n            H=900,\n            W=1600,\n            rand_flip=True,\n            rot3d_range=[-0.3925, 0.3925]),\n        test_mode=True,\n        tracking=True,\n        tracking_threshold=0.2))\noptimizer = dict(\n    type='AdamW',\n    lr=0.0006,\n    weight_decay=0.001,\n    paramwise_cfg=dict(custom_keys=dict(img_backbone=dict(lr_mult=0.5))))\noptimizer_config = dict(grad_clip=dict(max_norm=25, norm_type=2))\nlr_config = dict(\n    policy='CosineAnnealing',\n    warmup='linear',\n    warmup_iters=500,\n    warmup_ratio=0.3333333333333333,\n    min_lr_ratio=0.001)\nrunner = dict(type='IterBasedRunner', max_iters=2813000)\nvis_pipeline = [\n    dict(type='LoadMultiViewImageFromFiles', to_float32=True),\n    dict(type='Collect', keys=['img'], meta_keys=['timestamp', 'lidar2img'])\n]\nevaluation = dict(\n    interval=562600,\n    pipeline=[\n        dict(type='LoadMultiViewImageFromFiles', to_float32=True),\n        dict(\n            type='Collect', keys=['img'], meta_keys=['timestamp', 'lidar2img'])\n    ])\ngpu_ids = range(0, 1)\n", "seed": 0, "exp_name": "sparse4dv3_temporal_r50_1x8_bs6_256x704_local.py"}
{"mode": "train", "epoch": 1, "iter": 1, "lr": 0.0001, "memory": 2558, "data_time": 0.32594, "loss_cls_0": 2.41307, "loss_box_0": 0.05608, "loss_cns_0": 0.01418, "loss_yns_0": 0.00999, "loss_cls_1": 2.25198, "loss_box_1": 0.0, "loss_cns_1": 0.0, "loss_yns_1": 0.0, "loss_cls_2": 2.80785, "loss_box_2": 0.0, "loss_cns_2": 0.0, "loss_yns_2": 0.0, "loss_cls_3": 2.80916, "loss_box_3": 0.0, "loss_cns_3": 0.0, "loss_yns_3": 0.0, "loss_cls_4": 1.79493, "loss_box_4": 0.57156, "loss_cns_4": 0.0604, "loss_yns_4": 0.02789, "loss_cls_5": 2.09616, "loss_box_5": 0.0, "loss_cns_5": 0.0, "loss_yns_5": 0.0, "loss_cls_dn_0": 1.18384, "loss_box_dn_0": 1.49398, "loss_cls_dn_1": 1.10162, "loss_box_dn_1": 1.78668, "loss_cls_dn_2": 1.41841, "loss_box_dn_2": 2.10171, "loss_cls_dn_3": 1.20829, "loss_box_dn_3": 2.29093, "loss_cls_dn_4": 0.94449, "loss_box_dn_4": 2.38403, "loss_cls_dn_5": 1.11921, "loss_box_dn_5": 2.51601, "loss_dense_depth": 2.13702, "loss": 36.59945, "grad_norm": 537.70483, "time": 2.23974}
{"mode": "train", "epoch": 1, "iter": 2, "lr": 0.0001, "memory": 2845, "data_time": 0.0054, "loss_cls_0": 1.96773, "loss_box_0": 0.0, "loss_cns_0": 0.0, "loss_yns_0": 0.0, "loss_cls_1": 1.92191, "loss_box_1": 0.27385, "loss_cns_1": 0.05568, "loss_yns_1": 0.01195, "loss_cls_2": 2.25116, "loss_box_2": 0.07588, "loss_cns_2": 0.01246, "loss_yns_2": 0.00856, "loss_cls_3": 1.92622, "loss_box_3": 0.40936, "loss_cns_3": 0.04847, "loss_yns_3": 0.0067, "loss_cls_4": 1.44163, "loss_box_4": 1.80462, "loss_cns_4": 0.2217, "loss_yns_4": 0.06957, "loss_cls_5": 1.55532, "loss_box_5": 2.70723, "loss_cns_5": 0.3651, "loss_yns_5": 0.23206, "loss_cls_dn_0": 0.98769, "loss_box_dn_0": 1.33415, "loss_cls_dn_1": 0.8445, "loss_box_dn_1": 2.41414, "loss_cls_dn_2": 0.9737, "loss_box_dn_2": 2.43492, "loss_cls_dn_3": 0.82263, "loss_box_dn_3": 2.431, "loss_cls_dn_4": 0.69612, "loss_box_dn_4": 2.55456, "loss_cls_dn_5": 0.70508, "loss_box_dn_5": 2.69193, "loss_dense_depth": 1.94573, "loss": 38.2033, "grad_norm": 101.05103, "time": 0.54637}
{"mode": "train", "epoch": 1, "iter": 3, "lr": 0.0001, "memory": 2847, "data_time": 0.00567, "loss_cls_0": 1.30502, "loss_box_0": 3.13836, "loss_cns_0": 0.75539, "loss_yns_0": 0.20037, "loss_cls_1": 1.45412, "loss_box_1": 1.82958, "loss_cns_1": 0.30097, "loss_yns_1": 0.14288, "loss_cls_2": 1.64234, "loss_box_2": 0.47683, "loss_cns_2": 0.05647, "loss_yns_2": 0.02311, "loss_cls_3": 1.31337, "loss_box_3": 3.85185, "loss_cns_3": 0.43206, "loss_yns_3": 0.16593, "loss_cls_4": 0.99489, "loss_box_4": 4.72695, "loss_cns_4": 0.46172, "loss_yns_4": 0.1827, "loss_cls_5": 1.1058, "loss_box_5": 4.47628, "loss_cns_5": 0.42127, "loss_yns_5": 0.18466, "loss_cls_dn_0": 0.66476, "loss_box_dn_0": 1.28585, "loss_cls_dn_1": 0.66632, "loss_box_dn_1": 2.42759, "loss_cls_dn_2": 0.70765, "loss_box_dn_2": 2.36534, "loss_cls_dn_3": 0.57274, "loss_box_dn_3": 2.37237, "loss_cls_dn_4": 0.42585, "loss_box_dn_4": 2.50838, "loss_cls_dn_5": 0.47599, "loss_box_dn_5": 2.68068, "loss_dense_depth": 1.92173, "loss": 48.71819, "grad_norm": 143.26137, "time": 0.54587}
{"mode": "train", "epoch": 1, "iter": 4, "lr": 0.0001, "memory": 2847, "data_time": 0.0055, "loss_cls_0": 1.09301, "loss_box_0": 2.56665, "loss_cns_0": 0.6017, "loss_yns_0": 0.16747, "loss_cls_1": 1.16811, "loss_box_1": 2.65212, "loss_cns_1": 0.5778, "loss_yns_1": 0.1898, "loss_cls_2": 1.13481, "loss_box_2": 2.86852, "loss_cns_2": 0.4445, "loss_yns_2": 0.24767, "loss_cls_3": 0.95143, "loss_box_3": 4.26437, "loss_cns_3": 0.43024, "loss_yns_3": 0.2901, "loss_cls_4": 0.98403, "loss_box_4": 4.90405, "loss_cns_4": 0.36532, "loss_yns_4": 0.21339, "loss_cls_5": 1.17738, "loss_box_5": 5.20122, "loss_cns_5": 0.42018, "loss_yns_5": 0.18693, "loss_cls_dn_0": 0.55309, "loss_box_dn_0": 1.23802, "loss_cls_dn_1": 0.60285, "loss_box_dn_1": 2.2126, "loss_cls_dn_2": 0.59107, "loss_box_dn_2": 2.24962, "loss_cls_dn_3": 0.46181, "loss_box_dn_3": 2.59238, "loss_cls_dn_4": 0.41329, "loss_box_dn_4": 2.91851, "loss_cls_dn_5": 0.41178, "loss_box_dn_5": 3.16579, "loss_dense_depth": 1.88253, "loss": 52.39416, "grad_norm": 141.82367, "time": 0.5439}
{"mode": "train", "epoch": 1, "iter": 5, "lr": 0.0001, "memory": 2847, "data_time": 0.00558, "loss_cls_0": 1.14097, "loss_box_0": 2.60214, "loss_cns_0": 0.507, "loss_yns_0": 0.17055, "loss_cls_1": 0.98718, "loss_box_1": 3.51959, "loss_cns_1": 0.4897, "loss_yns_1": 0.1903, "loss_cls_2": 0.93209, "loss_box_2": 3.34495, "loss_cns_2": 0.45216, "loss_yns_2": 0.17898, "loss_cls_3": 0.86625, "loss_box_3": 3.16837, "loss_cns_3": 0.50432, "loss_yns_3": 0.19349, "loss_cls_4": 1.05825, "loss_box_4": 3.39539, "loss_cns_4": 0.47256, "loss_yns_4": 0.21823, "loss_cls_5": 0.98916, "loss_box_5": 4.00835, "loss_cns_5": 0.43966, "loss_yns_5": 0.23104, "loss_cls_dn_0": 0.50373, "loss_box_dn_0": 1.24732, "loss_cls_dn_1": 0.52765, "loss_box_dn_1": 2.65799, "loss_cls_dn_2": 0.47259, "loss_box_dn_2": 2.74043, "loss_cls_dn_3": 0.3786, "loss_box_dn_3": 2.94529, "loss_cls_dn_4": 0.35479, "loss_box_dn_4": 3.05535, "loss_cls_dn_5": 0.4073, "loss_box_dn_5": 3.27145, "loss_dense_depth": 1.72413, "loss": 50.34731, "grad_norm": 114.29729, "time": 0.53604}
{"mode": "train", "epoch": 1, "iter": 6, "lr": 0.0001, "memory": 2847, "data_time": 0.00584, "loss_cls_0": 1.03183, "loss_box_0": 2.68406, "loss_cns_0": 0.44033, "loss_yns_0": 0.16288, "loss_cls_1": 1.05111, "loss_box_1": 3.25286, "loss_cns_1": 0.44856, "loss_yns_1": 0.16482, "loss_cls_2": 1.09365, "loss_box_2": 3.19756, "loss_cns_2": 0.40777, "loss_yns_2": 0.17755, "loss_cls_3": 0.98536, "loss_box_3": 2.91777, "loss_cns_3": 0.48595, "loss_yns_3": 0.16349, "loss_cls_4": 0.88839, "loss_box_4": 3.07928, "loss_cns_4": 0.5707, "loss_yns_4": 0.24245, "loss_cls_5": 0.93513, "loss_box_5": 3.05124, "loss_cns_5": 0.55884, "loss_yns_5": 0.17395, "loss_cls_dn_0": 0.48488, "loss_box_dn_0": 1.28863, "loss_cls_dn_1": 0.43388, "loss_box_dn_1": 2.39197, "loss_cls_dn_2": 0.35872, "loss_box_dn_2": 2.4458, "loss_cls_dn_3": 0.32539, "loss_box_dn_3": 2.53045, "loss_cls_dn_4": 0.33412, "loss_box_dn_4": 2.52088, "loss_cls_dn_5": 0.40841, "loss_box_dn_5": 2.55415, "loss_dense_depth": 1.71642, "loss": 45.95924, "grad_norm": 153.38635, "time": 0.53993}
{"mode": "train", "epoch": 1, "iter": 7, "lr": 0.0001, "memory": 2847, "data_time": 0.00581, "loss_cls_0": 0.97447, "loss_box_0": 2.44797, "loss_cns_0": 0.54842, "loss_yns_0": 0.17379, "loss_cls_1": 1.04336, "loss_box_1": 3.36549, "loss_cns_1": 0.40733, "loss_yns_1": 0.19414, "loss_cls_2": 1.06065, "loss_box_2": 3.18872, "loss_cns_2": 0.50331, "loss_yns_2": 0.16548, "loss_cls_3": 0.96545, "loss_box_3": 2.75558, "loss_cns_3": 0.60199, "loss_yns_3": 0.18465, "loss_cls_4": 0.93242, "loss_box_4": 2.88995, "loss_cns_4": 0.57754, "loss_yns_4": 0.18137, "loss_cls_5": 0.89663, "loss_box_5": 2.94799, "loss_cns_5": 0.54783, "loss_yns_5": 0.19934, "loss_cls_dn_0": 0.45636, "loss_box_dn_0": 1.23242, "loss_cls_dn_1": 0.44231, "loss_box_dn_1": 2.14458, "loss_cls_dn_2": 0.36316, "loss_box_dn_2": 2.12455, "loss_cls_dn_3": 0.31632, "loss_box_dn_3": 2.05175, "loss_cls_dn_4": 0.35541, "loss_box_dn_4": 2.06786, "loss_cls_dn_5": 0.38626, "loss_box_dn_5": 2.02952, "loss_dense_depth": 1.50154, "loss": 43.22589, "grad_norm": 110.04499, "time": 0.54002}
