#!/usr/bin/env python3
"""
测试反射强度可视化功能的脚本

使用方法:
python test_intensity_visualization.py [sample_index] [colormap]

参数:
- sample_index: 要可视化的样本索引 (默认: 0)
- colormap: 颜色映射方案 (默认: jet)
  可选值: jet, hot, cool, viridis

示例:
python test_intensity_visualization.py 0 jet
python test_intensity_visualization.py 1 hot
python test_intensity_visualization.py 2 viridis
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from wenyuan_converter import Wenyuan2HV, label_list, root_path

def main():
    # 解析命令行参数
    sample_index = 0
    colormap = 'jet'
    
    if len(sys.argv) > 1:
        try:
            sample_index = int(sys.argv[1])
        except ValueError:
            print(f"错误: 样本索引必须是整数，得到: {sys.argv[1]}")
            return
    
    if len(sys.argv) > 2:
        colormap = sys.argv[2]
        if colormap not in ['jet', 'hot', 'cool', 'viridis']:
            print(f"错误: 不支持的颜色映射 '{colormap}'")
            print("支持的颜色映射: jet, hot, cool, viridis")
            return
    
    print("=" * 60)
    print("反射强度可视化测试")
    print("=" * 60)
    print(f"样本索引: {sample_index}")
    print(f"颜色映射: {colormap}")
    print()
    
    # 创建转换器实例
    try:
        converter = Wenyuan2HV(
            root_path=root_path,
            label_list=label_list,
            with_camera=True,
            with_lidar=True
        )
        
        if sample_index >= len(converter.samples):
            print(f"错误: 样本索引 {sample_index} 超出范围 (最大: {len(converter.samples)-1})")
            return
        
        print(f"找到 {len(converter.samples)} 个样本")
        print()
        
        # 显示颜色映射说明
        colormap_descriptions = {
            'jet': '蓝->青->绿->黄->红 (经典热力图)',
            'hot': '黑->红->黄->白 (热力图)',
            'cool': '青->紫 (冷色调)',
            'viridis': '紫->蓝->绿->黄 (感知均匀)'
        }
        
        print(f"使用 {colormap} 颜色映射: {colormap_descriptions[colormap]}")
        print()
        
        # 可视化样本
        sample_info = converter.debug_visualize_sample(
            sample_index=sample_index,
            save_images=False,
            show_3d=True,
            color_mode='intensity',
            colormap=colormap
        )
        
        if sample_info:
            print()
            print("可视化完成!")
            print(f"样本信息:")
            print(f"  - 时间戳: {sample_info['token']}")
            print(f"  - 场景: {sample_info['scene_token']}")
            print(f"  - 3D框数量: {len(sample_info['gt_boxes'])}")
            print(f"  - 相机数量: {len(sample_info['cams_info'])}")
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

def show_help():
    """显示帮助信息"""
    print(__doc__)
    
    print("\n可用的颜色映射方案:")
    print("┌──────────┬─────────────────────────────────┐")
    print("│ 颜色映射 │ 描述                            │")
    print("├──────────┼─────────────────────────────────┤")
    print("│ jet      │ 蓝->青->绿->黄->红 (经典热力图) │")
    print("│ hot      │ 黑->红->黄->白 (热力图)         │")
    print("│ cool     │ 青->紫 (冷色调)                 │")
    print("│ viridis  │ 紫->蓝->绿->黄 (感知均匀)       │")
    print("└──────────┴─────────────────────────────────┘")
    
    print("\n反射强度可视化说明:")
    print("- 低反射强度的点 (如道路表面) 显示为冷色调")
    print("- 高反射强度的点 (如金属表面、反光标志) 显示为暖色调")
    print("- 中等反射强度的点显示为中间色调")
    print()
    print("操作说明:")
    print("- 鼠标拖拽: 旋转视角")
    print("- 鼠标滚轮: 缩放")
    print("- Ctrl+鼠标拖拽: 平移")
    print("- 按 'Q' 或关闭窗口退出")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] in ['-h', '--help', 'help']:
        show_help()
    else:
        main()
